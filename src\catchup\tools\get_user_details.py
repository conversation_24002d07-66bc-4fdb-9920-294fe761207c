"""Read table data tool with summarization capabilities."""

from typing import Optional, Any, List, Tuple, Annotated
from langchain_core.runnables import RunnableConfig
from langchain_core.tools import tool, InjectedToolArg
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.config import get_stream_writer
import json
from pydantic import BaseModel
from datetime import datetime

from catchup.supabase.client import supabase

class UserDetails(BaseModel):
    id: str
    email: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone_number: str | None = None
    # Add other fields based on your user_details table schema

@tool
def get_user_details_by_id(
    user_id: str,) -> Tuple[Optional[UserDetails], Optional[str]]:
    """Fetch user table data from a specified table.
    
    Args:
        user_id: The ID of the user to get data from
    Returns:
        Tuple containing (user_data, error) where user_data is a validated 
        UserDetails model if successful, None if failed.
    """
    try:
        stream_writer = get_stream_writer()

        stream_writer({"custom_tool_call" : "invoking get_user_details_by_id for {user_id}"})
        response = supabase.table('user_details').select('*').eq('id', user_id).maybe_single().execute()
        
        # Extract data from response
        if response.data:
            user_data = UserDetails(**response.data[0])  # Assuming single user
            return user_data, None
        else:
            return None, "No user found with the provided ID"
            
    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)


