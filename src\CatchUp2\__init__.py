"""
CatchUp2 Agent - Enhanced Multi-tenant Marketplace Customer Service System

This module provides a new implementation of the CatchUp agent using the research_agent
architectural pattern while maintaining all existing functionality for multi-tenant
marketplace customer service.

Key Features:
- Multi-tenant marketplace support
- Deal and booking management
- User communication (email/WhatsApp)
- Category and service search
- Enhanced query processing
- JSON response formatting with required fields

Architecture:
- Based on research_agent pattern using create_deep_agent
- Specialized sub-agents for different marketplace functions
- Simplified state management while preserving functionality
- Tool integration through deepagents framework
"""

from .catchup2_agent import (
    agent,
    create_catchup2_agent,
    create_catchup2_agent_with_full_tools,
    get_agent,
    initialize_default_agent,
    get_agent_info,
    validate_agent_configuration
)
from .state import CatchUp2State, create_catchup2_state
from .prompts import *
from .tools import *

__all__ = [
    'agent',
    'create_catchup2_agent',
    'create_catchup2_agent_with_full_tools',
    'get_agent',
    'initialize_default_agent',
    'get_agent_info',
    'validate_agent_configuration',
    'CatchUp2State',
    'create_catchup2_state',
]
