"""
CatchUp2 Communication Tools

This module contains tools for communication operations including email sending,
WhatsApp messaging, plan and progress communication, and notification management
for the marketplace platform.

All tools maintain compatibility with the existing CatchUp system while providing
enhanced functionality for user communication and engagement.
"""

import json
from typing import List, Dict, Any, Optional
from datetime import datetime
from langchain_core.tools import tool
from shared.logger import logger
from ..supabase.client import get_supabase_client


@tool
def send_email_to_user(user_id: str, subject: str, content: str, email_type: str = "notification", booking_id: Optional[str] = None, deal_id: Optional[str] = None) -> str:
    """
    Send a professionally formatted HTML email to a user.
    
    This tool sends emails for various purposes including booking confirmations,
    promotional offers, notifications, and other relevant communications. It ensures
    branding consistency, personalization, and mobile-friendly design.
    
    Args:
        user_id: ID of the user to send email to
        subject: Email subject line
        content: Email content (can include HTML)
        email_type: Type of email (notification, booking, promotion, etc.)
        booking_id: Optional booking ID for booking-related emails
        deal_id: Optional deal ID for deal-related emails
        
    Returns:
        JSON string containing email sending result
    """
    try:
        logger.info(f"Sending email to user {user_id}, type: {email_type}")
        
        # Get database client
        client = get_supabase_client()
        
        # First, get user details to retrieve email address
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, email, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for email: {user_id}")
            return json.dumps({"error": "User not found", "success": False})
        
        user = user_result['data'][0]
        email_address = user.get('email')
        
        if not email_address:
            logger.warning(f"No email address found for user {user_id}")
            return json.dumps({"error": "User email address not available", "success": False})
        
        # Check user preferences for email communications
        preferences = user.get('preferences', {})
        if preferences.get('email_notifications') == False and email_type != 'booking':
            logger.info(f"User {user_id} has disabled email notifications")
            return json.dumps({"error": "User has disabled email notifications", "success": False})
        
        # Create email record in database
        email_data = {
            'user_id': user_id,
            'email_address': email_address,
            'subject': subject,
            'content': content,
            'email_type': email_type,
            'booking_id': booking_id,
            'deal_id': deal_id,
            'status': 'pending',
            'created_at': 'now()'
        }
        
        # Store email in database
        result = client.execute_query(
            table='email_queue',
            operation='insert',
            data=email_data
        )
        
        if result.get('error'):
            logger.error(f"Database error storing email: {result['error']}")
            return json.dumps({"error": "Failed to queue email", "success": False})
        
        # Here you would integrate with your actual email service (SendGrid, AWS SES, etc.)
        # For now, we'll simulate successful sending
        
        # Update email status to sent
        email_id = result.get('data', [{}])[0].get('id') if result.get('data') else None
        if email_id:
            client.execute_query(
                table='email_queue',
                operation='update',
                data={'status': 'sent', 'sent_at': 'now()'},
                filters=[{'column': 'id', 'value': email_id}]
            )
        
        logger.info(f"Successfully sent email to {email_address}")
        
        return json.dumps({
            "success": True,
            "user_id": user_id,
            "email_address": email_address,
            "subject": subject,
            "email_type": email_type,
            "message": "Email sent successfully"
        })
        
    except Exception as e:
        logger.error(f"Error in send_email_to_user: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def send_whatsapp_message(user_id: str, message: str, message_type: str = "notification", booking_id: Optional[str] = None, deal_id: Optional[str] = None) -> str:
    """
    Send a WhatsApp message to a user.
    
    This tool sends WhatsApp messages for quick notifications, booking confirmations,
    and other time-sensitive communications. It ensures messages are concise,
    friendly, and appropriate for the WhatsApp platform.
    
    Args:
        user_id: ID of the user to send message to
        message: Message content (plain text)
        message_type: Type of message (notification, booking, promotion, etc.)
        booking_id: Optional booking ID for booking-related messages
        deal_id: Optional deal ID for deal-related messages
        
    Returns:
        JSON string containing WhatsApp sending result
    """
    try:
        logger.info(f"Sending WhatsApp message to user {user_id}, type: {message_type}")
        
        # Get database client
        client = get_supabase_client()
        
        # First, get user details to retrieve phone number
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, phone, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for WhatsApp: {user_id}")
            return json.dumps({"error": "User not found", "success": False})
        
        user = user_result['data'][0]
        phone_number = user.get('phone')
        
        if not phone_number:
            logger.warning(f"No phone number found for user {user_id}")
            return json.dumps({
                "error": "Phone number not available. Please complete your profile in the CatchUp app.",
                "success": False
            })
        
        # Check user preferences for WhatsApp communications
        preferences = user.get('preferences', {})
        if preferences.get('whatsapp_notifications') == False and message_type != 'booking':
            logger.info(f"User {user_id} has disabled WhatsApp notifications")
            return json.dumps({"error": "User has disabled WhatsApp notifications", "success": False})
        
        # Ensure message doesn't contain sensitive IDs
        sanitized_message = message
        # Remove any potential ID references that shouldn't be visible to users
        sensitive_patterns = ['dealId', 'businessId', 'userId', 'sessionId']
        for pattern in sensitive_patterns:
            if pattern in sanitized_message:
                logger.warning(f"Removing sensitive information from WhatsApp message: {pattern}")
                # You might want to implement more sophisticated sanitization here
        
        # Create WhatsApp message record in database
        whatsapp_data = {
            'user_id': user_id,
            'phone_number': phone_number,
            'message': sanitized_message,
            'message_type': message_type,
            'booking_id': booking_id,
            'deal_id': deal_id,
            'status': 'pending',
            'created_at': 'now()'
        }
        
        # Store message in database
        result = client.execute_query(
            table='whatsapp_queue',
            operation='insert',
            data=whatsapp_data
        )
        
        if result.get('error'):
            logger.error(f"Database error storing WhatsApp message: {result['error']}")
            return json.dumps({"error": "Failed to queue WhatsApp message", "success": False})
        
        # Here you would integrate with your actual WhatsApp service (Twilio, WhatsApp Business API, etc.)
        # For now, we'll simulate successful sending
        
        # Update message status to sent
        message_id = result.get('data', [{}])[0].get('id') if result.get('data') else None
        if message_id:
            client.execute_query(
                table='whatsapp_queue',
                operation='update',
                data={'status': 'sent', 'sent_at': 'now()'},
                filters=[{'column': 'id', 'value': message_id}]
            )
        
        logger.info(f"Successfully sent WhatsApp message to {phone_number}")
        
        return json.dumps({
            "success": True,
            "user_id": user_id,
            "phone_number": phone_number,
            "message_type": message_type,
            "message": "WhatsApp message sent successfully"
        })
        
    except Exception as e:
        logger.error(f"Error in send_whatsapp_message: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def get_communication_preferences(user_id: str) -> str:
    """
    Get user's communication preferences.
    
    This tool retrieves the user's preferences for different types of communications
    including email, WhatsApp, and notification settings.
    
    Args:
        user_id: ID of the user to get preferences for
        
    Returns:
        JSON string containing communication preferences
    """
    try:
        logger.info(f"Fetching communication preferences for user {user_id}")
        
        # Get database client
        client = get_supabase_client()
        
        # Get user preferences
        result = client.execute_query(
            table='users',
            operation='select',
            columns='id, preferences, email, phone',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if result.get('error') or not result.get('data'):
            logger.warning(f"User not found: {user_id}")
            return json.dumps({"error": "User not found", "preferences": None})
        
        user = result['data'][0]
        preferences = user.get('preferences', {})
        
        # Extract communication-related preferences
        comm_preferences = {
            'user_id': user_id,
            'email_available': bool(user.get('email')),
            'phone_available': bool(user.get('phone')),
            'email_notifications': preferences.get('email_notifications', True),
            'whatsapp_notifications': preferences.get('whatsapp_notifications', True),
            'booking_confirmations': preferences.get('booking_confirmations', True),
            'promotional_emails': preferences.get('promotional_emails', False),
            'promotional_whatsapp': preferences.get('promotional_whatsapp', False),
            'preferred_communication': preferences.get('preferred_communication', 'email')
        }
        
        logger.info(f"Successfully fetched communication preferences for user {user_id}")
        
        return json.dumps({
            "success": True,
            "preferences": comm_preferences
        })
        
    except Exception as e:
        logger.error(f"Error in get_communication_preferences: {e}")
        return json.dumps({"error": str(e), "preferences": None})


@tool
def update_communication_preferences(user_id: str, preferences: Dict[str, Any]) -> str:
    """
    Update user's communication preferences.
    
    This tool allows users to update their communication preferences including
    email and WhatsApp notification settings.
    
    Args:
        user_id: ID of the user to update preferences for
        preferences: Dictionary containing preference updates
        
    Returns:
        JSON string containing update result
    """
    try:
        logger.info(f"Updating communication preferences for user {user_id}")
        
        # Get database client
        client = get_supabase_client()
        
        # Get current preferences
        current_result = client.execute_query(
            table='users',
            operation='select',
            columns='preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if current_result.get('error') or not current_result.get('data'):
            logger.warning(f"User not found: {user_id}")
            return json.dumps({"error": "User not found", "success": False})
        
        current_preferences = current_result['data'][0].get('preferences', {})
        
        # Merge communication preferences
        updated_preferences = {**current_preferences, **preferences}
        
        # Update preferences in database
        result = client.execute_query(
            table='users',
            operation='update',
            data={'preferences': updated_preferences, 'updated_at': 'now()'},
            filters=[{'column': 'id', 'value': user_id}]
        )
        
        if result.get('error'):
            logger.error(f"Database error updating preferences: {result['error']}")
            return json.dumps({"error": "Failed to update preferences", "success": False})
        
        logger.info(f"Successfully updated communication preferences for user {user_id}")
        
        return json.dumps({
            "success": True,
            "user_id": user_id,
            "updated_preferences": preferences,
            "message": "Communication preferences updated successfully"
        })

    except Exception as e:
        logger.error(f"Error in update_communication_preferences: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def send_plan_to_user(user_id: str, plan_title: str, tasks: List[Dict[str, Any]], estimated_duration: Optional[str] = None, communication_method: str = "auto") -> str:
    """
    Send a detailed plan to a user via their preferred communication method.

    This tool sends a comprehensive plan with task breakdown to users when complex
    multi-step operations are initiated. It formats the plan appropriately for
    the chosen communication channel and provides clear expectations.

    Args:
        user_id: ID of the user to send the plan to
        plan_title: Title/description of the overall plan
        tasks: List of tasks with structure [{"content": str, "status": str, "estimated_time": str}]
        estimated_duration: Optional estimated total duration for the plan
        communication_method: Method to use ("email", "whatsapp", "auto" for user preference)

    Returns:
        JSON string containing plan sending result
    """
    try:
        logger.info(f"Sending plan '{plan_title}' to user {user_id} via {communication_method}")

        # Get database client
        client = get_supabase_client()

        # Get user details and preferences
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, email, phone, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )

        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for plan sending: {user_id}")
            return json.dumps({"error": "User not found", "success": False})

        user_data = user_result['data'][0]
        user_name = user_data.get('name', 'User')
        user_preferences = user_data.get('preferences', {})

        # Determine communication method
        if communication_method == "auto":
            # Check user preferences for plan notifications
            preferred_method = user_preferences.get('plan_notifications', 'email')
            communication_method = preferred_method if preferred_method in ['email', 'whatsapp'] else 'email'

        # Format plan content
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')

        # Create task list with emojis and formatting
        task_list = []
        for i, task in enumerate(tasks, 1):
            status_emoji = {
                'pending': '⏳',
                'in_progress': '🔄',
                'completed': '✅'
            }.get(task.get('status', 'pending'), '📋')

            estimated_time = task.get('estimated_time', '')
            time_info = f" ({estimated_time})" if estimated_time else ""

            task_list.append(f"{i}. {status_emoji} {task['content']}{time_info}")

        tasks_formatted = "\n".join(task_list)

        # Send via appropriate method
        if communication_method == "email":
            subject = f"📋 Plan Created: {plan_title}"

            # Create HTML email content
            html_content = f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #2c3e50;">📋 Your Action Plan</h2>
                <p>Hi {user_name},</p>
                <p>I've created a detailed plan for your request: <strong>{plan_title}</strong></p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #495057; margin-top: 0;">📝 Plan Details</h3>
                    <p><strong>Created:</strong> {current_time}</p>
                    {f'<p><strong>Estimated Duration:</strong> {estimated_duration}</p>' if estimated_duration else ''}

                    <h4 style="color: #495057;">Tasks to Complete:</h4>
                    <div style="background-color: white; padding: 15px; border-radius: 5px;">
                        <pre style="font-family: Arial, sans-serif; white-space: pre-wrap; margin: 0;">{tasks_formatted}</pre>
                    </div>
                </div>

                <p>I'll keep you updated as I work through each task. You'll receive progress notifications as tasks are completed.</p>

                <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p style="margin: 0;"><strong>💡 What's Next:</strong> I'm starting work on this plan now. You'll receive updates as each task is completed.</p>
                </div>

                <p>Best regards,<br>CatchUp AI Assistant</p>
            </div>
            """

            return send_email_to_user.invoke({
                "user_id": user_id,
                "subject": subject,
                "content": html_content,
                "email_type": "plan_notification"
            })

        elif communication_method == "whatsapp":
            # Create concise WhatsApp message
            duration_text = f" (Est. {estimated_duration})" if estimated_duration else ""

            whatsapp_message = f"""📋 *Plan Created: {plan_title}*{duration_text}

Hi {user_name}! I've created a plan for your request:

{tasks_formatted}

I'll send you updates as I complete each task. Starting now! 🚀"""

            return send_whatsapp_message.invoke({
                "user_id": user_id,
                "message": whatsapp_message,
                "message_type": "plan_notification"
            })

        else:
            logger.error(f"Unsupported communication method: {communication_method}")
            return json.dumps({"error": f"Unsupported communication method: {communication_method}", "success": False})

    except Exception as e:
        logger.error(f"Error in send_plan_to_user: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def send_progress_update_to_user(user_id: str, task_completed: str, task_started: Optional[str] = None, progress_percentage: Optional[int] = None, communication_method: str = "auto") -> str:
    """
    Send a progress update to a user when a task is completed or started.

    This tool sends real-time progress updates to users as tasks in their plan
    are completed or started. It provides clear status updates and maintains
    engagement during complex operations.

    Args:
        user_id: ID of the user to send the update to
        task_completed: Description of the task that was just completed
        task_started: Optional description of the task that was just started
        progress_percentage: Optional overall progress percentage (0-100)
        communication_method: Method to use ("email", "whatsapp", "auto" for user preference)

    Returns:
        JSON string containing progress update sending result
    """
    try:
        logger.info(f"Sending progress update to user {user_id} - completed: {task_completed}")

        # Get database client
        client = get_supabase_client()

        # Get user details and preferences
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, email, phone, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )

        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for progress update: {user_id}")
            return json.dumps({"error": "User not found", "success": False})

        user_data = user_result['data'][0]
        user_name = user_data.get('name', 'User')
        user_preferences = user_data.get('preferences', {})

        # Determine communication method
        if communication_method == "auto":
            # Check user preferences for progress notifications
            preferred_method = user_preferences.get('progress_notifications', 'whatsapp')
            communication_method = preferred_method if preferred_method in ['email', 'whatsapp'] else 'whatsapp'

        # Format progress content
        current_time = datetime.now().strftime('%H:%M')

        # Create progress message
        progress_text = f" ({progress_percentage}% complete)" if progress_percentage is not None else ""

        if communication_method == "email":
            subject = f"✅ Progress Update{progress_text}"

            # Create HTML email content
            html_content = f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #2c3e50;">🔄 Progress Update</h2>
                <p>Hi {user_name},</p>

                <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
                    <h3 style="color: #155724; margin-top: 0;">✅ Task Completed</h3>
                    <p style="margin: 0; font-weight: bold;">{task_completed}</p>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em; color: #155724;">Completed at {current_time}</p>
                </div>

                {f'''
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
                    <h3 style="color: #856404; margin-top: 0;">🔄 Now Working On</h3>
                    <p style="margin: 0; font-weight: bold;">{task_started}</p>
                </div>
                ''' if task_started else ''}

                {f'''
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #495057; margin-top: 0;">📊 Overall Progress</h3>
                    <div style="background-color: #e9ecef; border-radius: 10px; height: 20px; margin: 10px 0;">
                        <div style="background-color: #28a745; height: 20px; border-radius: 10px; width: {progress_percentage}%; transition: width 0.3s ease;"></div>
                    </div>
                    <p style="margin: 5px 0 0 0; text-align: center; font-weight: bold;">{progress_percentage}% Complete</p>
                </div>
                ''' if progress_percentage is not None else ''}

                <p>I'll continue working on your request and send you updates as more tasks are completed.</p>

                <p>Best regards,<br>CatchUp AI Assistant</p>
            </div>
            """

            return send_email_to_user.invoke({
                "user_id": user_id,
                "subject": subject,
                "content": html_content,
                "email_type": "progress_update"
            })

        elif communication_method == "whatsapp":
            # Create concise WhatsApp message
            whatsapp_message = f"""✅ *Task Completed* ({current_time})
{task_completed}"""

            if task_started:
                whatsapp_message += f"""

🔄 *Now Working On:*
{task_started}"""

            if progress_percentage is not None:
                whatsapp_message += f"""

📊 *Progress:* {progress_percentage}% complete"""

            return send_whatsapp_message.invoke({
                "user_id": user_id,
                "message": whatsapp_message,
                "message_type": "progress_update"
            })

        else:
            logger.error(f"Unsupported communication method: {communication_method}")
            return json.dumps({"error": f"Unsupported communication method: {communication_method}", "success": False})

    except Exception as e:
        logger.error(f"Error in send_progress_update_to_user: {e}")
        return json.dumps({"error": str(e), "success": False})


@tool
def send_plan_completion_to_user(user_id: str, plan_title: str, summary: str, results: List[str], communication_method: str = "auto") -> str:
    """
    Send a plan completion notification to a user when all tasks are finished.

    This tool sends a comprehensive completion notification when a complex plan
    has been fully executed. It provides a summary of what was accomplished
    and any relevant results or next steps.

    Args:
        user_id: ID of the user to send the completion notification to
        plan_title: Title of the completed plan
        summary: Brief summary of what was accomplished
        results: List of key results or outcomes from the plan execution
        communication_method: Method to use ("email", "whatsapp", "auto" for user preference)

    Returns:
        JSON string containing completion notification sending result
    """
    try:
        logger.info(f"Sending plan completion notification for '{plan_title}' to user {user_id}")

        # Get database client
        client = get_supabase_client()

        # Get user details and preferences
        user_result = client.execute_query(
            table='users',
            operation='select',
            columns='id, email, phone, name, preferences',
            filters=[{'column': 'id', 'value': user_id}]
        )

        if user_result.get('error') or not user_result.get('data'):
            logger.warning(f"User not found for completion notification: {user_id}")
            return json.dumps({"error": "User not found", "success": False})

        user_data = user_result['data'][0]
        user_name = user_data.get('name', 'User')
        user_preferences = user_data.get('preferences', {})

        # Determine communication method
        if communication_method == "auto":
            # Check user preferences for completion notifications
            preferred_method = user_preferences.get('completion_notifications', 'email')
            communication_method = preferred_method if preferred_method in ['email', 'whatsapp'] else 'email'

        # Format completion content
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M')

        # Format results list
        results_formatted = "\n".join([f"• {result}" for result in results])

        if communication_method == "email":
            subject = f"🎉 Plan Completed: {plan_title}"

            # Create HTML email content
            html_content = f"""
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <h2 style="color: #2c3e50;">🎉 Plan Completed Successfully!</h2>
                <p>Hi {user_name},</p>
                <p>Great news! I've successfully completed your plan: <strong>{plan_title}</strong></p>

                <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
                    <h3 style="color: #155724; margin-top: 0;">📋 Summary</h3>
                    <p style="margin: 0;">{summary}</p>
                    <p style="margin: 10px 0 0 0; font-size: 0.9em; color: #155724;"><strong>Completed:</strong> {current_time}</p>
                </div>

                {f'''
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="color: #495057; margin-top: 0;">✅ Key Results</h3>
                    <div style="background-color: white; padding: 15px; border-radius: 5px;">
                        <pre style="font-family: Arial, sans-serif; white-space: pre-wrap; margin: 0;">{results_formatted}</pre>
                    </div>
                </div>
                ''' if results else ''}

                <div style="background-color: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <p style="margin: 0;"><strong>🎯 Mission Accomplished!</strong> All tasks have been completed successfully. If you need any additional assistance or have questions about the results, feel free to ask!</p>
                </div>

                <p>Thank you for using CatchUp! I'm here whenever you need assistance.</p>

                <p>Best regards,<br>CatchUp AI Assistant</p>
            </div>
            """

            return send_email_to_user.invoke({
                "user_id": user_id,
                "subject": subject,
                "content": html_content,
                "email_type": "completion_notification"
            })

        elif communication_method == "whatsapp":
            # Create concise WhatsApp message
            whatsapp_message = f"""🎉 *Plan Completed: {plan_title}*

Hi {user_name}! ✅ All done!

*Summary:* {summary}"""

            if results:
                whatsapp_message += f"""

*Key Results:*
{results_formatted}"""

            whatsapp_message += f"""

🎯 Mission accomplished! All tasks completed successfully. Need anything else? Just ask! 😊"""

            return send_whatsapp_message.invoke({
                "user_id": user_id,
                "message": whatsapp_message,
                "message_type": "completion_notification"
            })

        else:
            logger.error(f"Unsupported communication method: {communication_method}")
            return json.dumps({"error": f"Unsupported communication method: {communication_method}", "success": False})

    except Exception as e:
        logger.error(f"Error in send_plan_completion_to_user: {e}")
        return json.dumps({"error": str(e), "success": False})
