"""
CatchUp2 Supabase Client

This module provides Supabase database client functionality for the CatchUp2 agent.
It handles database connections, query execution, and data operations for the
multi-tenant marketplace system.

Key Features:
- Supabase client initialization and configuration
- Query execution with error handling
- Connection pooling and management
- Multi-tenant data isolation
"""

import os
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
from supabase import create_client, Client
from shared.logger import logger

load_dotenv()
class SupabaseClient:
    """
    Supabase client wrapper for CatchUp2 agent.
    
    Provides database connectivity and query execution capabilities
    with proper error handling and logging.
    """
    
    def __init__(self):
        """Initialize the Supabase client with environment configuration."""
        self.url = os.getenv('SUPABASE_URL')
      
        self.key = os.getenv('SUPABASE_SERVICE_ROLE_KEY') 
        self.client: Optional[Client] = None

        if not self.url or not self.key:
            logger.error("Supabase URL or key not found in environment variables")
            logger.error(f"SUPABASE_URL: {self.url}")
            logger.error(f"SUPABASE_SERVICE_ROLE_KEY: {os.getenv('SUPABASE_SERVICE_ROLE_KEY')}")
         
            raise ValueError("Missing Supabase configuration")
    
    def get_client(self) -> Client:
        """
        Get or create the Supabase client instance.
        
        Returns:
            Client: Configured Supabase client
        """
        if self.client is None:
            try:
                self.client = create_client(self.url, self.key)
                logger.info("Supabase client initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                raise
        
        return self.client
    
    def execute_query(self, table: str, operation: str, **kwargs) -> Dict[str, Any]:
        """
        Execute a database query with error handling.

        Args:
            table: Database table name
            operation: Operation type (select, insert, update, delete)
            **kwargs: Additional query parameters

        Returns:
            Dict containing query results or error information
        """
        try:
            client = self.get_client()

            if operation == 'select':
                result = client.table(table).select(kwargs.get('columns', '*'))
                if 'filters' in kwargs:
                    for filter_item in kwargs['filters']:
                        result = result.eq(filter_item['column'], filter_item['value'])
                response = result.execute()
                # Convert APIResponse to dict format
                return {
                    "data": response.data,
                    "count": response.count,
                    "success": True
                }

            elif operation == 'insert':
                response = client.table(table).insert(kwargs.get('data', {})).execute()
                return {
                    "data": response.data,
                    "count": response.count,
                    "success": True
                }

            elif operation == 'update':
                result = client.table(table).update(kwargs.get('data', {}))
                if 'filters' in kwargs:
                    for filter_item in kwargs['filters']:
                        result = result.eq(filter_item['column'], filter_item['value'])
                response = result.execute()
                return {
                    "data": response.data,
                    "count": response.count,
                    "success": True
                }

            elif operation == 'delete':
                result = client.table(table)
                if 'filters' in kwargs:
                    for filter_item in kwargs['filters']:
                        result = result.eq(filter_item['column'], filter_item['value'])
                response = result.delete().execute()
                return {
                    "data": response.data,
                    "count": response.count,
                    "success": True
                }

            else:
                raise ValueError(f"Unsupported operation: {operation}")

        except Exception as e:
            logger.error(f"Database query failed: {e}")
            return {"error": str(e), "success": False}


# Global client instance
_supabase_client = None


def get_supabase_client() -> SupabaseClient:
    """
    Get the global Supabase client instance.

    Returns:
        SupabaseClient: Configured client instance
    """
    global _supabase_client
    if _supabase_client is None:
        _supabase_client = SupabaseClient()
    return _supabase_client


def execute_query(table: str, operation: str, **kwargs) -> Dict[str, Any]:
    """
    Convenience function for executing database queries.

    Args:
        table: Database table name
        operation: Operation type (select, insert, update, delete)
        **kwargs: Additional query parameters

    Returns:
        Dict containing query results or error information
    """
    client = get_supabase_client()
    return client.execute_query(table, operation, **kwargs)


# Direct Supabase client access for CatchUp compatibility
def get_direct_supabase_client():
    """
    Get direct Supabase client for CatchUp-style queries.

    Returns:
        Client: Direct Supabase client instance
    """
    client = get_supabase_client()
    return client.get_client()


# Create a global supabase instance for CatchUp compatibility
supabase = None

def initialize_supabase():
    """Initialize the global supabase client for CatchUp compatibility."""
    global supabase
    if supabase is None:
        supabase = get_direct_supabase_client()
    return supabase

# Initialize on import
supabase = initialize_supabase()
