"""
Test Plan and Update Communication System

This module contains tests to verify that the enhanced CatchUp2 agent properly
sends plans and progress updates to users via their preferred communication channels.

Test Scenarios:
1. Plan creation and notification sending
2. Progress updates during task execution
3. Plan completion notifications
4. Communication method preferences
5. Error handling and fallbacks
"""

import asyncio
import j<PERSON>
from typing import Dict, Any, List
from unittest.mock import Mock, patch
import pytest

from CatchUp2.tools.communication_tools import (
    send_plan_to_user,
    send_progress_update_to_user,
    send_plan_completion_to_user
)
from CatchUp2.tools.planning_tools import (
    write_todos_with_notification,
    update_todo_with_notification,
    complete_plan_with_notification
)
from CatchUp2.state import CatchUp2State, create_catchup2_state
from CatchUp2.streaming import ProgressStreamer, create_progress_streamer
from shared.logger import logger


class TestPlanCommunication:
    """Test suite for plan and update communication functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.test_user_id = "test_user_123"
        self.test_session_id = "test_session_456"
        self.test_state = create_catchup2_state(
            user_id=self.test_user_id,
            session_id=self.test_session_id,
            email_address="<EMAIL>",
            is_chat=False
        )
    
    @patch('CatchUp2.tools.communication_tools.get_supabase_client')
    def test_send_plan_to_user_email(self, mock_client):
        """Test sending plan notification via email."""
        # Mock database responses for plan notification
        mock_client.return_value.execute_query.side_effect = [
            # First call: get user details for plan notification
            {
                'data': [{
                    'id': self.test_user_id,
                    'email': '<EMAIL>',
                    'name': 'Test User',
                    'preferences': {'plan_notifications': 'email'}
                }],
                'error': None
            },
            # Second call: get user details for email sending
            {
                'data': [{
                    'id': self.test_user_id,
                    'email': '<EMAIL>',
                    'name': 'Test User',
                    'preferences': {'plan_notifications': 'email'}
                }],
                'error': None
            },
            {'data': [{'id': 'email_123'}], 'error': None},  # Email insert
            {'data': [], 'error': None}  # Email update
        ]
        
        # Test plan sending
        tasks = [
            {"content": "Search for haircut deals", "status": "pending"},
            {"content": "Book haircut appointment", "status": "pending"},
            {"content": "Send confirmation", "status": "pending"}
        ]
        
        result = send_plan_to_user.invoke({
            "user_id": self.test_user_id,
            "plan_title": "Book Haircut Service",
            "tasks": tasks,
            "estimated_duration": "15 minutes",
            "communication_method": "email"
        })
        
        # Verify result
        result_data = json.loads(result)
        assert result_data['success'] is True
        assert result_data['user_id'] == self.test_user_id
        assert result_data['email_type'] == 'plan_notification'

        # Verify database calls (plan notification + email sending)
        assert mock_client.return_value.execute_query.call_count == 4
    
    @patch('CatchUp2.tools.communication_tools.get_supabase_client')
    def test_send_plan_to_user_whatsapp(self, mock_client):
        """Test sending plan notification via WhatsApp."""
        # Mock database responses
        mock_client.return_value.execute_query.side_effect = [
            # First call: get user details for plan notification
            {
                'data': [{
                    'id': self.test_user_id,
                    'phone': '+1234567890',
                    'name': 'Test User',
                    'preferences': {'plan_notifications': 'whatsapp'}
                }],
                'error': None
            },
            # Second call: get user details for WhatsApp sending
            {
                'data': [{
                    'id': self.test_user_id,
                    'phone': '+1234567890',
                    'name': 'Test User',
                    'preferences': {'plan_notifications': 'whatsapp'}
                }],
                'error': None
            },
            {'data': [{'id': 'whatsapp_123'}], 'error': None},  # WhatsApp insert
            {'data': [], 'error': None}  # WhatsApp update
        ]
        
        # Test plan sending
        tasks = [
            {"content": "Search for deals", "status": "pending"},
            {"content": "Make booking", "status": "pending"}
        ]
        
        result = send_plan_to_user.invoke({
            "user_id": self.test_user_id,
            "plan_title": "Quick Booking",
            "tasks": tasks,
            "communication_method": "whatsapp"
        })
        
        # Verify result
        result_data = json.loads(result)
        assert result_data['success'] is True
        assert result_data['user_id'] == self.test_user_id
        assert result_data['message_type'] == 'plan_notification'
    
    @patch('CatchUp2.tools.communication_tools.get_supabase_client')
    def test_send_progress_update(self, mock_client):
        """Test sending progress update notification."""
        # Mock database responses
        mock_client.return_value.execute_query.side_effect = [
            # First call: get user details for progress notification
            {
                'data': [{
                    'id': self.test_user_id,
                    'phone': '+1234567890',
                    'name': 'Test User',
                    'preferences': {'progress_notifications': 'whatsapp'}
                }],
                'error': None
            },
            # Second call: get user details for WhatsApp sending
            {
                'data': [{
                    'id': self.test_user_id,
                    'phone': '+1234567890',
                    'name': 'Test User',
                    'preferences': {'progress_notifications': 'whatsapp'}
                }],
                'error': None
            },
            {'data': [{'id': 'progress_123'}], 'error': None},  # Progress insert
            {'data': [], 'error': None}  # Progress update
        ]
        
        # Test progress update
        result = send_progress_update_to_user.invoke({
            "user_id": self.test_user_id,
            "task_completed": "Search for haircut deals",
            "task_started": "Book haircut appointment",
            "progress_percentage": 50,
            "communication_method": "whatsapp"
        })
        
        # Verify result
        result_data = json.loads(result)
        assert result_data['success'] is True
        assert result_data['message_type'] == 'progress_update'
    
    @patch('CatchUp2.tools.communication_tools.get_supabase_client')
    def test_send_completion_notification(self, mock_client):
        """Test sending plan completion notification."""
        # Mock database responses
        mock_client.return_value.execute_query.side_effect = [
            # First call: get user details for completion notification
            {
                'data': [{
                    'id': self.test_user_id,
                    'email': '<EMAIL>',
                    'name': 'Test User',
                    'preferences': {'completion_notifications': 'email'}
                }],
                'error': None
            },
            # Second call: get user details for email sending
            {
                'data': [{
                    'id': self.test_user_id,
                    'email': '<EMAIL>',
                    'name': 'Test User',
                    'preferences': {'completion_notifications': 'email'}
                }],
                'error': None
            },
            {'data': [{'id': 'completion_123'}], 'error': None},  # Completion insert
            {'data': [], 'error': None}  # Completion update
        ]
        
        # Test completion notification
        result = send_plan_completion_to_user.invoke({
            "user_id": self.test_user_id,
            "plan_title": "Book Haircut Service",
            "summary": "Successfully booked haircut appointment for tomorrow at 2 PM",
            "results": [
                "Found 5 available haircut deals",
                "Booked appointment at StyleCut Salon",
                "Sent confirmation email and WhatsApp message"
            ],
            "communication_method": "email"
        })
        
        # Verify result
        result_data = json.loads(result)
        assert result_data['success'] is True
        assert result_data['email_type'] == 'completion_notification'
    
    def test_progress_streamer_creation(self):
        """Test creating and using progress streamer."""
        streamer = create_progress_streamer(
            user_id=self.test_user_id,
            session_id=self.test_session_id
        )
        
        assert streamer.user_id == self.test_user_id
        assert streamer.session_id == self.test_session_id
        assert streamer.current_step == 0
        assert streamer.total_steps == 0
    
    @patch('CatchUp2.streaming.get_stream_writer')
    def test_progress_streamer_workflow(self, mock_stream_writer):
        """Test complete progress streaming workflow."""
        # Mock stream writer
        mock_writer = Mock()
        mock_stream_writer.return_value = mock_writer
        
        # Create streamer and test workflow
        streamer = create_progress_streamer(user_id=self.test_user_id)
        
        # Start plan
        tasks = ["Task 1", "Task 2", "Task 3"]
        streamer.start_plan("Test Plan", len(tasks), tasks)
        
        # Start and complete tasks
        streamer.start_task("Task 1")
        streamer.complete_task("Task 1", "Task 1 completed", "Task 2")
        
        streamer.start_task("Task 2")
        streamer.complete_task("Task 2", "Task 2 completed", "Task 3")
        
        streamer.start_task("Task 3")
        streamer.complete_task("Task 3", "Task 3 completed")
        
        # Complete plan
        streamer.complete_plan(
            "Test Plan",
            "All tasks completed successfully",
            ["Result 1", "Result 2", "Result 3"]
        )
        
        # Verify stream writer was called
        assert mock_writer.call_count > 0
    
    def test_error_handling_invalid_user(self):
        """Test error handling when user is not found."""
        with patch('CatchUp2.tools.communication_tools.get_supabase_client') as mock_client:
            # Mock user not found
            mock_client.return_value.execute_query.return_value = {
                'data': [],
                'error': None
            }
            
            result = send_plan_to_user.invoke({
                "user_id": "invalid_user",
                "plan_title": "Test Plan",
                "tasks": [{"content": "Test task", "status": "pending"}]
            })
            
            result_data = json.loads(result)
            assert result_data['success'] is False
            assert "User not found" in result_data['error']
    
    def test_auto_communication_method_selection(self):
        """Test automatic communication method selection based on user preferences."""
        with patch('CatchUp2.tools.communication_tools.get_supabase_client') as mock_client:
            # Mock user with email preference
            mock_client.return_value.execute_query.side_effect = [
                # First call: get user details for plan notification
                {
                    'data': [{
                        'id': self.test_user_id,
                        'email': '<EMAIL>',
                        'name': 'Test User',
                        'preferences': {'plan_notifications': 'email'}
                    }],
                    'error': None
                },
                # Second call: get user details for email sending
                {
                    'data': [{
                        'id': self.test_user_id,
                        'email': '<EMAIL>',
                        'name': 'Test User',
                        'preferences': {'plan_notifications': 'email'}
                    }],
                    'error': None
                },
                {'data': [{'id': 'email_123'}], 'error': None},
                {'data': [], 'error': None}
            ]
            
            # Test auto method selection
            result = send_plan_to_user.invoke({
                "user_id": self.test_user_id,
                "plan_title": "Auto Method Test",
                "tasks": [{"content": "Test task", "status": "pending"}],
                "communication_method": "auto"
            })
            
            result_data = json.loads(result)
            assert result_data['success'] is True
            assert result_data['email_type'] == 'plan_notification'


async def run_integration_test():
    """
    Run integration test with actual agent to verify end-to-end functionality.
    
    This test creates a CatchUp2 agent and simulates a complex operation
    to verify that plans and updates are properly sent to users.
    """
    try:
        from CatchUp2.catchup2_agent import create_catchup2_agent_with_full_tools
        
        logger.info("Starting integration test for plan communication")
        
        # Create agent
        agent = create_catchup2_agent_with_full_tools()
        
        # Create test state
        test_state = create_catchup2_state(
            user_id="integration_test_user",
            session_id="integration_test_session",
            email_address="<EMAIL>",
            is_chat=False
        )
        
        # Test message that should trigger plan creation
        test_message = {
            "messages": [{
                "role": "user",
                "content": "I need to book both a haircut and manicure for this weekend, and send me confirmations via email and WhatsApp"
            }],
            **test_state
        }
        
        logger.info("Invoking agent with complex booking request")
        
        # This should trigger the enhanced planning tools
        # Note: In a real test, you would mock the database and communication services
        # result = await agent.ainvoke(test_message)
        
        logger.info("Integration test completed - agent should have created plan and sent notifications")
        
        return True
        
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        return False


if __name__ == "__main__":
    """Run tests when script is executed directly."""
    
    # Run unit tests
    test_suite = TestPlanCommunication()
    test_suite.setup_method()
    
    print("Running plan communication tests...")
    
    try:
        test_suite.test_send_plan_to_user_email()
        print("✅ Email plan notification test passed")
        
        test_suite.test_send_plan_to_user_whatsapp()
        print("✅ WhatsApp plan notification test passed")
        
        test_suite.test_send_progress_update()
        print("✅ Progress update test passed")
        
        test_suite.test_send_completion_notification()
        print("✅ Completion notification test passed")
        
        test_suite.test_progress_streamer_creation()
        print("✅ Progress streamer creation test passed")
        
        test_suite.test_progress_streamer_workflow()
        print("✅ Progress streamer workflow test passed")
        
        test_suite.test_error_handling_invalid_user()
        print("✅ Error handling test passed")
        
        test_suite.test_auto_communication_method_selection()
        print("✅ Auto communication method test passed")
        
        print("\n🎉 All unit tests passed!")
        
        # Run integration test
        print("\nRunning integration test...")
        integration_result = asyncio.run(run_integration_test())
        
        if integration_result:
            print("✅ Integration test passed")
        else:
            print("❌ Integration test failed")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
