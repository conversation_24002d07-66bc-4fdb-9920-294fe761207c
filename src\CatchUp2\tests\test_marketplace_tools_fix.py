"""
Test marketplace tools with InjectedState pattern
"""

import pytest
from unittest.mock import Mock, patch
from CatchUp2.tools.marketplace_tools import search_deals, get_deals_by_categoryId
from CatchUp2.state import create_catchup2_state
from langgraph.managed import IsLastStep, RemainingSteps


def test_search_deals_with_state():
    """Test that search_deals works with InjectedState pattern"""
    # Create test state with required AgentState fields
    test_state = create_catchup2_state(
        user_id="test_user_123",
        session_id="test_session"
    )
    # Add required AgentState fields
    test_state["is_last_step"] = IsLastStep(False)
    test_state["remaining_steps"] = RemainingSteps(5)
    
    # Mock the supabase response
    mock_response = Mock()
    mock_response.data = [
        {
            'id': '1',  # String ID as expected by Deal model
            'title': 'Test Deal',
            'description': 'Test Description',
            'price': 50.0,
            'category_name': 'Beauty',
            'business_name': 'Test Business',
            'owner_id': 'other_user'
        }
    ]
    
    with patch('CatchUp2.tools.marketplace_tools.supabase') as mock_supabase, \
         patch('CatchUp2.tools.marketplace_tools.get_stream_writer') as mock_stream_writer:

        mock_supabase.table.return_value.select.return_value.eq.return_value.neq.return_value.execute.return_value = mock_response
        mock_stream_writer.return_value = Mock()

        # Test the tool function directly with state
        deals, error = search_deals.func("Beauty", state=test_state)
        
        assert error is None
        assert len(deals) == 1
        assert deals[0].title == 'Test Deal'
        
        # Verify that neq was called with the user_id to exclude user's own deals
        mock_supabase.table.return_value.select.return_value.eq.return_value.neq.assert_called_with('owner_id', 'test_user_123')


def test_search_deals_missing_user_id():
    """Test search_deals handles missing user_id in state"""
    # Create test state without user_id but with required AgentState fields
    test_state = {
        "session_id": "test_session",
        "messages": [],
        "is_last_step": IsLastStep(False),
        "remaining_steps": RemainingSteps(5)
    }
    
    deals, error = search_deals.func("Beauty", state=test_state)
    
    assert deals is None
    assert "user_id not found in state" in error


def test_get_deals_by_categoryId_with_state():
    """Test that get_deals_by_categoryId works with InjectedState pattern"""
    # Create test state with required AgentState fields
    test_state = create_catchup2_state(
        user_id="test_user_456",
        session_id="test_session"
    )
    # Add required AgentState fields
    test_state["is_last_step"] = IsLastStep(False)
    test_state["remaining_steps"] = RemainingSteps(5)
    
    # Mock the supabase response
    mock_response = Mock()
    mock_response.data = [
        {
            'id': '2',  # String ID as expected by Deal model
            'title': 'Category Deal',
            'description': 'Deal by category ID',
            'price': 75.0,
            'category_id': '1',
            'business_name': 'Test Business 2',
            'owner_id': 'other_user_2'
        }
    ]
    
    with patch('CatchUp2.tools.marketplace_tools.supabase') as mock_supabase, \
         patch('CatchUp2.tools.marketplace_tools.get_stream_writer') as mock_stream_writer:

        mock_supabase.table.return_value.select.return_value.eq.return_value.neq.return_value.execute.return_value = mock_response
        mock_stream_writer.return_value = Mock()

        # Test the tool function directly with state
        deals, error = get_deals_by_categoryId.func("1", state=test_state)
        
        assert error is None
        assert len(deals) == 1
        assert deals[0].title == 'Category Deal'
        
        # Verify that neq was called with the user_id to exclude user's own deals
        mock_supabase.table.return_value.select.return_value.eq.return_value.neq.assert_called_with('owner_id', 'test_user_456')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
