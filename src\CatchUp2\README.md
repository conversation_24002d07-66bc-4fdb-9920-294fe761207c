# CatchUp2 Agent

CatchUp2 is an advanced AI customer service agent for multi-tenant marketplace platforms, built on the research_agent architecture while maintaining all functionality of the original CatchUp system.

## Overview

CatchUp2 provides comprehensive customer service capabilities for marketplace operations including deal discovery, booking management, user communication, and business information services. It combines the simplicity of the research_agent pattern with the rich functionality required for multi-tenant marketplace customer service.

## Key Features

### 🏪 Multi-tenant Marketplace Support
- Business isolation and context-aware responses
- Deal and service discovery across multiple vendors
- Category-based organization and filtering
- Location-based recommendations

### 📅 Booking Management
- Complete booking lifecycle management (create, view, modify, cancel)
- Availability validation and time slot management
- Booking confirmations and status tracking
- Integration with business calendars and schedules

### 💬 Multi-channel Communication
- Professional email communications with HTML formatting
- WhatsApp messaging for instant notifications
- User communication preference management
- Privacy-compliant messaging with consent tracking

### 👤 User Management
- Comprehensive user profile management
- Preference and settings customization
- Conversation history and context preservation
- Location services for personalized recommendations

### 🤖 Advanced AI Architecture
- Research_agent pattern with automatic TODO planning for complex operations
- Specialized sub-agents for different marketplace functions
- Intelligent memory management for long conversations
- Simplified state structure matching original CatchUp agent
- JSON response formatting with required marketplace fields

### 📋 TODO Planning & Progress Tracking
- Automatic TODO list creation for complex multi-step operations
- Real-time progress streaming with visual indicators
- Task completion tracking following research_agent pattern
- Progress visibility for users during complex marketplace operations

## Architecture

### Core Components

```
CatchUp2/
├── catchup2_agent.py      # Main agent implementation
├── state.py               # State management and memory
├── prompts.py             # Prompt templates and instructions
├── sub_agents.py          # Specialized sub-agents
├── tools/                 # Tool implementations
│   ├── marketplace_tools.py
│   ├── user_tools.py
│   └── communication_tools.py
├── Models/                # Data models and schemas
├── supabase/             # Database integration
└── README.md             # This documentation
```

### Sub-agents

CatchUp2 uses specialized sub-agents for different marketplace functions:

- **booking-agent**: Handles booking creation, management, and modifications
- **deal-search-agent**: Manages deal discovery and filtering operations
- **communication-agent**: Handles email and WhatsApp communications
- **user-management-agent**: Manages user profiles and preferences
- **query-enhancement-agent**: Improves query understanding and processing

### State Management

The agent uses `CatchUp2State` which extends `DeepAgentState` with simplified structure:

- Enhanced conversation memory with intelligent message prioritization
- Simplified field structure matching original CatchUp agent
- Essential user and session context for multi-tenant support
- Compatible with both chat mode (markdown) and API mode (JSON) responses

**State Fields:**
- `messages`: Conversation history with intelligent memory management
- `user_id`: Unique user identifier (Required)
- `session_id`: Unique session identifier (Required)
- `email_address`: User's email address (Optional)
- `latitude`: User's latitude coordinate as string (Optional)
- `longitude`: User's longitude coordinate as string (Optional)
- `memory_length`: Memory budget as string (Optional)
- `isChat`: Response format - True for markdown, False for JSON (Optional)

## Installation and Setup

### Prerequisites

- Python 3.8+
- Required environment variables:
  - `OPENROUTER_API_KEY`: API key for LLM access
  - `SUPABASE_URL`: Supabase database URL
  - `SUPABASE_ANON_KEY`: Supabase anonymous key

### Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
export OPENROUTER_API_KEY="your_openrouter_api_key"
export SUPABASE_URL="your_supabase_url"
export SUPABASE_ANON_KEY="your_supabase_key"
```

3. Import and initialize the agent:
```python
from CatchUp2 import agent, create_catchup2_state

# Create initial state
state = create_catchup2_state(
    user_id="user123",
    session_id="session456",
    email_address="<EMAIL>",
    is_chat=True
)

# Use the agent
response = agent.invoke({"messages": [user_message]}, state)
```

## Usage Examples

### Basic Deal Search

```python
from langchain_core.messages import HumanMessage

# Create user message
user_message = HumanMessage(content="I'm looking for a haircut in Milan")

# Create state with simplified structure
state = create_catchup2_state(
    user_id="user123",
    session_id="session456",
    latitude=45.4642,
    longitude=9.1900,
    is_chat=True  # Markdown responses
)

# Get response
response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

### Booking Creation

```python
# User wants to book a specific deal
user_message = HumanMessage(content="I want to book the haircut deal for tomorrow at 3 PM")

response = agent.invoke({"messages": [user_message]}, state)
```

### Communication Preferences

```python
# User wants to receive booking confirmation via WhatsApp
user_message = HumanMessage(content="Please send my booking confirmation via WhatsApp")

response = agent.invoke({"messages": [user_message]}, state)
```

### Complex Multi-Step Operations with TODO Planning

```python
# Complex operation that triggers automatic TODO planning
user_message = HumanMessage(content="I need both a haircut and manicure for this weekend, and send me confirmations via email and WhatsApp")

response = agent.invoke({"messages": [user_message]}, state)

# The agent will automatically:
# 1. Create a TODO list with progress tracking
# 2. Stream progress updates like:
#    "📋 Created plan with 6 tasks: search haircut deals, search manicure deals, ..."
#    "🔄 Starting: Search for haircut deals in user's area"
#    "✅ Completed: Search for haircut deals in user's area"
# 3. Execute each task systematically
# 4. Provide final comprehensive response
```

## API Documentation

### Main Agent Functions

#### `create_catchup2_agent(model_name, **kwargs)`
Creates the main CatchUp2 agent with full functionality.

**Parameters:**
- `model_name` (str): LLM model name (default: "anthropic/claude-3.5-sonnet")
- `**kwargs`: Additional LLM configuration parameters

**Returns:**
- Configured CatchUp2 agent ready for customer service operations

#### `create_simple_catchup2_agent(model_name, **kwargs)`
Creates a simplified version with essential tools only.

**Parameters:**
- `model_name` (str): LLM model name
- `**kwargs`: Additional LLM configuration parameters

**Returns:**
- Simplified CatchUp2 agent for basic operations

### State Management

#### `create_catchup2_state(user_id, session_id, **kwargs)`
Creates a new CatchUp2State instance with simplified structure.

**Parameters:**
- `user_id` (str): Unique user identifier
- `session_id` (str): Unique session identifier
- `email_address` (str, optional): User's email address
- `latitude` (float, optional): User's latitude coordinate (converted to string)
- `longitude` (float, optional): User's longitude coordinate (converted to string)
- `memory_length` (int, optional): Memory budget for conversation (converted to string)
- `is_chat` (bool): Whether responses should be markdown (True) or JSON (False)

**Returns:**
- Initialized CatchUp2State instance with simplified field structure

### Response Format

When `is_chat=False`, responses follow the required JSON format:

```json
{
  "llmResponse": "Natural language response in markdown format",
  "llmResponseIntent": "generic_chat|available_deals|booking_data",
  "userIntent": "find_service|book_service|view_bookings|ask_offer_info|greetings|goodbye|request_help|generic_chat|not_understood",
  "relatedIds": ["array of relevant bookingId or dealId"]
}
```

## Configuration

### Memory Management

The agent uses intelligent memory management with configurable budgets:

```python
# Configure memory budget in state
state = create_catchup2_state(
    user_id="user123",
    session_id="session456",
    memory_length=20  # Increase for longer conversations
)
```

### TODO Planning Configuration

TODO planning is automatically triggered for complex operations. The agent will create TODO lists for:

- Multi-service booking processes (e.g., booking both haircut and manicure)
- Deal search with multiple criteria and filtering steps
- Communication workflows involving multiple channels (email + WhatsApp)
- User profile updates with multiple preference changes

**Progress Streaming Messages:**
- Plan Creation: "📋 Created plan with [X] tasks: [brief list of task names]"
- Task Start: "🔄 Starting: [task description]"
- Task Completion: "✅ Completed: [task description]"

### Communication Preferences

Users can configure their communication preferences:

```python
# Update user communication preferences
preferences = {
    "email_notifications": True,
    "whatsapp_notifications": False,
    "preferred_communication": "email"
}
```

## Monitoring and Debugging

### Logging

CatchUp2 includes comprehensive logging for monitoring and debugging:

```python
from shared.logger import logger

# Enable debug logging
logger.setLevel("DEBUG")

# Monitor agent operations
response = agent.invoke({"messages": [user_message]}, state)
```

### Agent Information

Get detailed information about agent configuration:

```python
from CatchUp2.catchup2_agent import get_agent_info, validate_agent_configuration

# Get agent configuration details
info = get_agent_info()
print(f"Agent has {info['total_tools']} tools and {info['total_sub_agents']} sub-agents")

# Validate configuration
validation = validate_agent_configuration()
if validation["status"] != "success":
    print(f"Configuration issues: {validation['issues']}")
```

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   - Ensure all required environment variables are set
   - Check API key validity and permissions

2. **Database Connection Issues**
   - Verify Supabase URL and key configuration
   - Check network connectivity and firewall settings

3. **Memory Budget Exceeded**
   - Increase memory_length in state configuration
   - Monitor conversation length and complexity

4. **Tool Execution Failures**
   - Check database connectivity and data integrity
   - Verify user permissions and data access rights

### Debug Mode

Enable debug mode for detailed logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

When contributing to CatchUp2:

1. Follow the existing code structure and patterns
2. Add comprehensive comments explaining business logic
3. Include proper error handling and logging
4. Update documentation for new features
5. Test with various marketplace scenarios

## License

[Add your license information here]
