from pydantic import BaseModel


class Category(BaseModel):
    """Category model."""
    categoryId: str
    name: str
    description: str
    icon: str

class UserDetails(BaseModel):
    """User details model."""
    id: str
    email: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone_number: str | None = None
    # Add other fields based on your user_details table schema

class Deal(BaseModel):
    """Deal model for deals_dealid_view."""
    id: str
    title: str | None = None
    description: str | None = None
    category_id: str | None = None
    owner_id: str | None = None
    price: float | None = None
    discount_percentage: float | None = None
    business_name: str | None = None
    location: str | None = None
    availability: str | None = None
    # Add other fields based on your deals_dealid_view schema