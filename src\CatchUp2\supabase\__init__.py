"""
CatchUp2 Supabase Integration Module

This module provides database connectivity and operations for the CatchUp2 agent
using Supabase as the backend database system.

Features:
- Database client configuration
- Query execution utilities
- Data access layer for marketplace entities
- Connection management and error handling
"""

from .client import *

__all__ = [
    'get_supabase_client',
    'execute_query',
]
