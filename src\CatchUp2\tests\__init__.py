"""
CatchUp2 Tests Module

This module contains test suites for the CatchUp2 agent functionality,
including plan communication, progress updates, and user notifications.

Test Categories:
- Plan and update communication tests
- Tool integration tests
- Agent workflow tests
- Error handling and edge case tests
"""

from .test_plan_communication import TestPlanCommunication, run_integration_test

__all__ = [
    'TestPlanCommunication',
    'run_integration_test'
]
