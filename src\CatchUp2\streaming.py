"""
CatchUp2 Progress Streaming

This module provides real-time progress streaming functionality that sends updates
to users as tasks are completed, integrating with LangGraph's streaming capabilities
and the communication tools.

Key Features:
- Real-time progress streaming during plan execution
- Integration with LangGraph stream writers
- Automatic user notifications via preferred communication channels
- Progress visualization and status updates
"""

import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from langgraph.config import get_stream_writer
from shared.logger import logger


class ProgressStreamer:
    """
    Progress streaming manager for CatchUp2 operations.
    
    This class manages real-time progress updates during complex operations,
    sending both internal streaming updates and external user notifications.
    """
    
    def __init__(self, user_id: Optional[str] = None, session_id: Optional[str] = None):
        """
        Initialize the progress streamer.
        
        Args:
            user_id: ID of the user to send notifications to
            session_id: Session ID for tracking
        """
        self.user_id = user_id
        self.session_id = session_id
        self.start_time = datetime.now()
        self.current_step = 0
        self.total_steps = 0
        
    def start_plan(self, plan_title: str, total_steps: int, tasks: List[str]) -> None:
        """
        Start a new plan and send initial progress update.
        
        Args:
            plan_title: Title of the plan being executed
            total_steps: Total number of steps in the plan
            tasks: List of task descriptions
        """
        self.total_steps = total_steps
        self.current_step = 0
        
        logger.info(f"Starting plan '{plan_title}' with {total_steps} steps")
        
        # Send internal streaming update
        self._send_stream_update({
            "type": "plan_started",
            "plan_title": plan_title,
            "total_steps": total_steps,
            "tasks": tasks,
            "timestamp": datetime.now().isoformat()
        })
        
        # Send user notification if user_id is available
        if self.user_id:
            try:
                from .tools.communication_tools import send_plan_to_user
                
                # Format tasks for notification
                notification_tasks = [
                    {"content": task, "status": "pending"} 
                    for task in tasks
                ]
                
                send_plan_to_user.invoke({
                    "user_id": self.user_id,
                    "plan_title": plan_title,
                    "tasks": notification_tasks,
                    "communication_method": "auto"
                })
                
                logger.info(f"Plan notification sent to user {self.user_id}")
                
            except Exception as e:
                logger.error(f"Failed to send plan notification: {e}")
    
    def start_task(self, task_description: str, step_number: Optional[int] = None) -> None:
        """
        Mark a task as started and send progress update.
        
        Args:
            task_description: Description of the task being started
            step_number: Optional step number (will auto-increment if not provided)
        """
        if step_number is not None:
            self.current_step = step_number
        else:
            self.current_step += 1
        
        progress_percentage = int((self.current_step / self.total_steps) * 100) if self.total_steps > 0 else 0
        
        logger.info(f"Starting task {self.current_step}/{self.total_steps}: {task_description}")
        
        # Send internal streaming update
        self._send_stream_update({
            "type": "task_started",
            "task_description": task_description,
            "step_number": self.current_step,
            "total_steps": self.total_steps,
            "progress_percentage": progress_percentage,
            "timestamp": datetime.now().isoformat()
        })
        
        # Send progress message
        self._send_progress_message(f"🔄 Starting: {task_description}", progress_percentage)
    
    def complete_task(self, task_description: str, result: Optional[str] = None, next_task: Optional[str] = None) -> None:
        """
        Mark a task as completed and send progress update.
        
        Args:
            task_description: Description of the completed task
            result: Optional result or outcome of the task
            next_task: Optional description of the next task to start
        """
        progress_percentage = int((self.current_step / self.total_steps) * 100) if self.total_steps > 0 else 0
        
        logger.info(f"Completed task {self.current_step}/{self.total_steps}: {task_description}")
        
        # Send internal streaming update
        self._send_stream_update({
            "type": "task_completed",
            "task_description": task_description,
            "result": result,
            "step_number": self.current_step,
            "total_steps": self.total_steps,
            "progress_percentage": progress_percentage,
            "next_task": next_task,
            "timestamp": datetime.now().isoformat()
        })
        
        # Send progress message
        self._send_progress_message(f"✅ Completed: {task_description}", progress_percentage)
        
        # Send user notification if user_id is available
        if self.user_id:
            try:
                from .tools.communication_tools import send_progress_update_to_user
                
                send_progress_update_to_user.invoke({
                    "user_id": self.user_id,
                    "task_completed": task_description,
                    "task_started": next_task,
                    "progress_percentage": progress_percentage,
                    "communication_method": "auto"
                })
                
                logger.info(f"Progress notification sent to user {self.user_id}")
                
            except Exception as e:
                logger.error(f"Failed to send progress notification: {e}")
    
    def complete_plan(self, plan_title: str, summary: str, results: List[str]) -> None:
        """
        Mark the entire plan as completed and send final notification.
        
        Args:
            plan_title: Title of the completed plan
            summary: Summary of what was accomplished
            results: List of key results or outcomes
        """
        end_time = datetime.now()
        duration = end_time - self.start_time
        
        logger.info(f"Completed plan '{plan_title}' in {duration}")
        
        # Send internal streaming update
        self._send_stream_update({
            "type": "plan_completed",
            "plan_title": plan_title,
            "summary": summary,
            "results": results,
            "duration_seconds": duration.total_seconds(),
            "timestamp": end_time.isoformat()
        })
        
        # Send completion message
        self._send_progress_message(f"🎉 Plan Completed: {plan_title}", 100)
        
        # Send user notification if user_id is available
        if self.user_id:
            try:
                from .tools.communication_tools import send_plan_completion_to_user
                
                send_plan_completion_to_user.invoke({
                    "user_id": self.user_id,
                    "plan_title": plan_title,
                    "summary": summary,
                    "results": results,
                    "communication_method": "auto"
                })
                
                logger.info(f"Plan completion notification sent to user {self.user_id}")
                
            except Exception as e:
                logger.error(f"Failed to send completion notification: {e}")
    
    def send_status_update(self, message: str, status_type: str = "info") -> None:
        """
        Send a general status update.
        
        Args:
            message: Status message to send
            status_type: Type of status (info, warning, error, success)
        """
        logger.info(f"Status update ({status_type}): {message}")
        
        # Send internal streaming update
        self._send_stream_update({
            "type": "status_update",
            "message": message,
            "status_type": status_type,
            "timestamp": datetime.now().isoformat()
        })
        
        # Send progress message with appropriate emoji
        emoji_map = {
            "info": "ℹ️",
            "warning": "⚠️",
            "error": "❌",
            "success": "✅"
        }
        emoji = emoji_map.get(status_type, "📢")
        self._send_progress_message(f"{emoji} {message}")
    
    def _send_stream_update(self, data: Dict[str, Any]) -> None:
        """
        Send internal streaming update using LangGraph stream writer.
        
        Args:
            data: Data to stream
        """
        try:
            writer = get_stream_writer()
            if writer:
                writer({
                    "type": "progress_update",
                    "data": data,
                    "user_id": self.user_id,
                    "session_id": self.session_id
                })
        except Exception as e:
            logger.debug(f"Stream writer not available or failed: {e}")
    
    def _send_progress_message(self, message: str, progress: Optional[int] = None) -> None:
        """
        Send a formatted progress message.
        
        Args:
            message: Progress message
            progress: Optional progress percentage
        """
        try:
            writer = get_stream_writer()
            if writer:
                progress_data = {
                    "type": "progress_message",
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                }
                
                if progress is not None:
                    progress_data["progress"] = progress
                
                writer(progress_data)
        except Exception as e:
            logger.debug(f"Progress message streaming failed: {e}")


def create_progress_streamer(user_id: Optional[str] = None, session_id: Optional[str] = None) -> ProgressStreamer:
    """
    Factory function to create a progress streamer instance.
    
    Args:
        user_id: ID of the user to send notifications to
        session_id: Session ID for tracking
        
    Returns:
        Configured ProgressStreamer instance
    """
    return ProgressStreamer(user_id=user_id, session_id=session_id)


# Convenience functions for quick progress updates
def stream_plan_start(plan_title: str, tasks: List[str], user_id: Optional[str] = None) -> ProgressStreamer:
    """
    Quick function to start streaming a plan.
    
    Args:
        plan_title: Title of the plan
        tasks: List of task descriptions
        user_id: Optional user ID for notifications
        
    Returns:
        ProgressStreamer instance for continued use
    """
    streamer = create_progress_streamer(user_id=user_id)
    streamer.start_plan(plan_title, len(tasks), tasks)
    return streamer


def stream_task_progress(message: str, progress: Optional[int] = None) -> None:
    """
    Quick function to send a task progress update.
    
    Args:
        message: Progress message
        progress: Optional progress percentage
    """
    try:
        writer = get_stream_writer()
        if writer:
            writer({
                "type": "task_progress",
                "message": message,
                "progress": progress,
                "timestamp": datetime.now().isoformat()
            })
    except Exception as e:
        logger.debug(f"Task progress streaming failed: {e}")


def stream_completion(message: str) -> None:
    """
    Quick function to send a completion message.
    
    Args:
        message: Completion message
    """
    try:
        writer = get_stream_writer()
        if writer:
            writer({
                "type": "completion",
                "message": message,
                "timestamp": datetime.now().isoformat()
            })
    except Exception as e:
        logger.debug(f"Completion streaming failed: {e}")
