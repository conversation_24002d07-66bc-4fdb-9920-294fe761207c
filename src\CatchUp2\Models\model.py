"""
CatchUp2 Data Models

This module defines the data models and schemas used throughout the CatchUp2 agent
for representing marketplace entities and managing data consistency.

Models include:
- Deal: Marketplace deals and offers (compatible with CatchUp)
- Category: Service categories (compatible with CatchUp)
- UserDetails: User profile information (compatible with CatchUp)
- Booking: User bookings and reservations
- Business: Business/merchant information
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from datetime import datetime
from enum import Enum


class BookingStatus(Enum):
    """Enumeration of possible booking statuses."""
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"


class DealStatus(Enum):
    """Enumeration of possible deal statuses."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"


class Category(BaseModel):
    """Category model - compatible with CatchUp."""
    categoryId: str
    name: str
    description: str
    icon: str


class UserDetails(BaseModel):
    """User details model - compatible with CatchUp."""
    id: str
    email: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone_number: str | None = None
    # Add other fields based on your user_details table schema


class Deal(BaseModel):
    """Deal model for deals_dealid_view - compatible with CatchUp."""
    id: str
    title: str | None = None
    description: str | None = None
    category_id: str | None = None
    owner_id: str | None = None
    price: float | None = None
    discount_percentage: float | None = None
    business_name: str | None = None
    location: str | None = None
    availability: str | None = None
    # Add other fields based on your deals_dealid_view schema


# Additional models for CatchUp2 functionality (using dataclasses for internal use)
from dataclasses import dataclass

@dataclass
class TimeSlot:
    """Represents a time slot for availability."""
    start_time: str  # Format: "HH:MM"
    end_time: str    # Format: "HH:MM"


@dataclass
class Booking:
    """Represents a user booking."""
    id: str
    user_id: str
    deal_id: str
    business_id: str
    booking_date: str  # ISO date string
    booking_time: Optional[str]  # Format: "HH:MM"
    status: BookingStatus
    notes: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class User:
    """Represents a user profile."""
    id: str
    email: Optional[str]
    phone: Optional[str]
    name: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    preferences: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class Business:
    """Represents a business/merchant."""
    id: str
    name: str
    description: Optional[str]
    address: Optional[str]
    phone: Optional[str]
    email: Optional[str]
    latitude: Optional[float]
    longitude: Optional[float]
    categories: Optional[List[str]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class CommunicationRequest:
    """Represents a communication request (email/WhatsApp)."""
    user_id: str
    method: str  # "email" or "whatsapp"
    content: str
    subject: Optional[str] = None
    booking_id: Optional[str] = None
    deal_id: Optional[str] = None
