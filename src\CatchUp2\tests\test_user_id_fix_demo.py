"""
Demo test showing that the user_id issue is fixed
"""

import pytest
from unittest.mock import Mock, patch
from CatchUp2.tools.marketplace_tools import search_deals
from CatchUp2.state import create_catchup2_state
from langgraph.managed import IsLastStep, RemainingSteps


def test_user_id_fix_demo():
    """
    Demo test showing that the original issue is fixed.
    
    Before the fix: Tools required user_id as a direct parameter like search_deals(category_name, user_id)
    After the fix: Tools get user_id from state using InjectedState pattern
    
    This resolves the issue where the system was trying to pass "current_user" as user_id.
    """
    # Create test state with a real user_id (not "current_user")
    test_state = create_catchup2_state(
        user_id="real_user_123",  # This is the actual user_id that should be used
        session_id="test_session"
    )
    # Add required AgentState fields
    test_state["is_last_step"] = IsLastStep(False)
    test_state["remaining_steps"] = RemainingSteps(5)
    
    # Mock the supabase response
    mock_response = Mock()
    mock_response.data = [
        {
            'id': '1',
            'title': 'Launch Bar Aperitivo Deal',
            'description': 'Great aperitivo deal',
            'price': 15.0,
            'category_name': 'Launch Bar',
            'business_name': 'Aperitivo Bar',
            'owner_id': 'different_user'  # Different from our user
        }
    ]
    
    with patch('CatchUp2.tools.marketplace_tools.supabase') as mock_supabase, \
         patch('CatchUp2.tools.marketplace_tools.get_stream_writer') as mock_stream_writer:
        
        mock_supabase.table.return_value.select.return_value.eq.return_value.neq.return_value.execute.return_value = mock_response
        mock_stream_writer.return_value = Mock()
        
        # This now works! The tool gets user_id from state instead of requiring it as parameter
        deals, error = search_deals.func("Launch Bar", state=test_state)
        
        # Verify the fix works
        assert error is None
        assert len(deals) == 1
        assert deals[0].title == 'Launch Bar Aperitivo Deal'
        
        # Verify that the correct user_id was used to exclude user's own deals
        mock_supabase.table.return_value.select.return_value.eq.return_value.neq.assert_called_with('owner_id', 'real_user_123')
        
        print("✅ Fix verified: Tools now correctly access user_id from state!")
        print(f"✅ User ID used: real_user_123 (not 'current_user')")
        print(f"✅ Found {len(deals)} deals for Launch Bar category")


if __name__ == "__main__":
    test_user_id_fix_demo()
    print("Demo completed successfully!")
