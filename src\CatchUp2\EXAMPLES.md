# CatchUp2 Usage Examples

This document provides comprehensive examples of how to use the CatchUp2 agent for various marketplace customer service scenarios.

## Table of Contents

1. [Basic Setup](#basic-setup)
2. [Deal Discovery Examples](#deal-discovery-examples)
3. [Booking Management Examples](#booking-management-examples)
4. [Communication Examples](#communication-examples)
5. [User Management Examples](#user-management-examples)
6. [Advanced Scenarios](#advanced-scenarios)

## Basic Setup

### Environment Setup

```python
import os
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage

# Load environment variables
load_dotenv()

# Required environment variables
os.environ["OPENROUTER_API_KEY"] = "your_openrouter_api_key"
os.environ["SUPABASE_URL"] = "your_supabase_url"
os.environ["SUPABASE_ANON_KEY"] = "your_supabase_key"
```

### Agent Initialization

```python
from CatchUp2 import agent, create_catchup2_state

# Create user state
state = create_catchup2_state(
    user_id="user_12345",
    session_id="session_67890",
    email_address="<EMAIL>",
    latitude=45.4642,  # Milan coordinates
    longitude=9.1900,
    is_chat=True  # For markdown responses
)

print("CatchUp2 agent initialized successfully!")
```

## Deal Discovery Examples

### Example 1: Basic Category Search

```python
# User looking for beauty services
user_message = HumanMessage(content="I'm looking for beauty services in Milan")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
I found several beauty services available in Milan:

## Available Beauty Deals

1. **Premium Haircut & Styling** at Salon Eleganza
   - 25% discount (€37.50 instead of €50.00)
   - Available: Monday-Friday, 9:00-18:00
   - Location: Via Brera 15, Milan

2. **Manicure & Pedicure Package** at Beauty Center Milano
   - 30% discount (€35.00 instead of €50.00)
   - Available: Tuesday-Saturday, 10:00-19:00
   - Location: Corso Buenos Aires 42, Milan

Would you like to book any of these services or see more options?
```

### Example 2: Specific Service with Time Preference

```python
# User with specific requirements
user_message = HumanMessage(content="Cerco una manicure a Milano zona duomo con sconto non inferiore al 20% per il giorno 22 Agosto dopo le 16:40")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
Ho trovato queste opzioni per una manicure nella zona Duomo di Milano per il 22 Agosto dopo le 16:40:

## Offerte Manicure Disponibili

1. **Manicure Professionale** presso Beauty Studio Duomo
   - Sconto del 25% (€22.50 invece di €30.00)
   - Disponibile: 22 Agosto, 17:00-19:00
   - Indirizzo: Piazza Duomo 12, Milano

2. **Manicure Deluxe** presso Nail Art Center
   - Sconto del 30% (€28.00 invece di €40.00)
   - Disponibile: 22 Agosto, 16:45-18:30
   - Indirizzo: Via Torino 8, Milano

Vuoi prenotare uno di questi servizi?
```

### Example 3: Category Exploration

```python
# User wants to see all available categories
user_message = HumanMessage(content="What categories of services do you offer?")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

## Booking Management Examples

### Example 4: Creating a Booking

```python
# User wants to book a specific service
user_message = HumanMessage(content="I want to book the Premium Haircut at Salon Eleganza for tomorrow at 2 PM")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
Perfect! I've created your booking for the Premium Haircut at Salon Eleganza.

## Booking Confirmation

- **Service**: Premium Haircut & Styling
- **Business**: Salon Eleganza
- **Date**: February 16, 2024
- **Time**: 14:00 (2:00 PM)
- **Price**: €37.50 (25% discount applied)
- **Status**: Confirmed

**Business Details:**
- Address: Via Brera 15, Milan
- Phone: +39 02 1234567

Your booking has been confirmed! Would you like me to send you a confirmation email or WhatsApp message?
```

### Example 5: Viewing Booking History

```python
# User wants to see their bookings
user_message = HumanMessage(content="Show me my recent bookings")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
Here are your recent bookings:

## Your Bookings

### Upcoming Bookings
1. **Premium Haircut** at Salon Eleganza
   - Date: February 16, 2024 at 14:00
   - Status: Confirmed
   - Price: €37.50

### Past Bookings
1. **Manicure Package** at Beauty Center Milano
   - Date: February 10, 2024 at 15:30
   - Status: Completed
   - Price: €35.00

2. **Facial Treatment** at Wellness Spa
   - Date: February 5, 2024 at 11:00
   - Status: Completed
   - Price: €45.00

Would you like to modify any of these bookings or book a new service?
```

### Example 6: Booking Modification

```python
# User wants to change booking time
user_message = HumanMessage(content="Can I change my haircut appointment to 3 PM instead?")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

## Communication Examples

### Example 7: Email Confirmation Request

```python
# User wants email confirmation
user_message = HumanMessage(content="Please send me an email confirmation for my haircut booking")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
I've sent a booking confirmation <NAME_EMAIL> with all the details of your haircut appointment.

The email includes:
- Booking details and timing
- Business contact information
- Directions to the salon
- Cancellation policy

You should receive it within a few minutes. Please check your spam folder if you don't see it in your inbox.
```

### Example 8: WhatsApp Notification

```python
# User prefers WhatsApp
user_message = HumanMessage(content="Send me a WhatsApp reminder for my appointment tomorrow")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
I've sent a WhatsApp reminder to your phone number ending in 7890.

The message includes:
- Appointment details (Premium Haircut at 2 PM)
- Business address and contact
- Reminder to arrive 10 minutes early

You'll receive the message shortly on WhatsApp.
```

### Example 9: Communication Preferences

```python
# User wants to update preferences
user_message = HumanMessage(content="I prefer to receive notifications via email only, not WhatsApp")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

## User Management Examples

### Example 10: Profile Information

```python
# User wants to see their profile
user_message = HumanMessage(content="Show me my profile information")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
## Your Profile

**Personal Information:**
- Name: John Doe
- Email: <EMAIL>
- Phone: +39 ************
- Member since: January 1, 2024

**Location:**
- Current location: Milan, Italy (45.4642, 9.1900)

**Preferences:**
- Language: English
- Preferred communication: Email
- Email notifications: Enabled
- WhatsApp notifications: Disabled

**Account Status:** Active

Would you like to update any of this information?
```

### Example 11: Location Update

```python
# User moved to a new location
user_message = HumanMessage(content="I've moved to Rome, can you update my location?")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

## Advanced Scenarios

### Example 12: Multi-step Booking Process

```python
# Complex booking scenario
messages = [
    HumanMessage(content="I need a haircut and manicure for this weekend"),
    # Agent responds with options
    HumanMessage(content="I'll take the haircut on Saturday at 2 PM and manicure on Sunday at 11 AM"),
    # Agent creates bookings
    HumanMessage(content="Send me email confirmations for both appointments")
]

for message in messages:
    response = agent.invoke({"messages": [message]}, state)
    print(f"User: {message.content}")
    print(f"Agent: {response['messages'][-1].content}\n")
```

### Example 13: Error Handling

```python
# User requests unavailable service
user_message = HumanMessage(content="Book me a massage for today at 1 PM")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
I understand you'd like to book a massage for today at 1 PM. Unfortunately, I don't see any available massage appointments for that time slot today.

## Alternative Options:

1. **Tomorrow (February 16)** - Several massage slots available:
   - 13:00 at Wellness Spa Milano
   - 14:30 at Relax Center
   - 16:00 at Spa Luxury

2. **Different time today**:
   - 18:00 at Evening Wellness Center
   - 19:30 at After Hours Spa

Would you like me to book one of these alternatives, or would you prefer to see more options for different days?
```

### Example 14: JSON Response Mode

```python
# Configure for API mode
api_state = create_catchup2_state(
    user_id="user_12345",
    session_id="session_67890",
    is_chat=False  # JSON responses
)

user_message = HumanMessage(content="Find me beauty deals in Milan")
response = agent.invoke({"messages": [user_message]}, api_state)
print(response["messages"][-1].content)
```

**Expected Response:**
```json
{
  "llmResponse": "I found 5 beauty deals available in Milan:\n\n1. **Premium Haircut** at Salon Eleganza - 25% discount (€37.50)\n2. **Manicure Package** at Beauty Center - 30% discount (€35.00)\n3. **Facial Treatment** at Wellness Spa - 20% discount (€45.00)\n\nAll deals are available for booking with various time slots throughout the week.",
  "llmResponseIntent": "available_deals",
  "userIntent": "find_service",
  "relatedIds": ["deal_123", "deal_456", "deal_789"]
}
```

### Example 15: Sub-agent Delegation

```python
# Complex query requiring multiple sub-agents
user_message = HumanMessage(content="Find me a haircut deal, book it for tomorrow, and send me a WhatsApp confirmation")

response = agent.invoke({"messages": [user_message]}, state)
print(response["messages"][-1].content)
```

**Expected Response:**
```markdown
I've completed all your requests:

## 1. Found Haircut Deal
**Premium Haircut** at Salon Eleganza
- 25% discount (€37.50 instead of €50.00)
- Available tomorrow at multiple time slots

## 2. Created Booking
- **Date**: February 16, 2024
- **Time**: 14:00 (first available slot)
- **Status**: Confirmed
- **Booking ID**: BOOK_789

## 3. WhatsApp Confirmation Sent
A confirmation message has been sent to your WhatsApp with:
- Complete booking details
- Business contact information
- Directions and parking info

Everything is set up for your appointment tomorrow!
```

## Testing and Validation

### Example 16: Agent Configuration Validation

```python
from CatchUp2.catchup2_agent import validate_agent_configuration, get_agent_info

# Validate configuration
validation = validate_agent_configuration()
print(f"Configuration Status: {validation['status']}")

if validation['issues']:
    print("Issues found:")
    for issue in validation['issues']:
        print(f"  - {issue}")

# Get agent information
info = get_agent_info()
print(f"\nAgent Info:")
print(f"  - Total Tools: {info['total_tools']}")
print(f"  - Sub-agents: {info['total_sub_agents']}")
print(f"  - Features: {', '.join(info['features'][:3])}...")
```

These examples demonstrate the comprehensive capabilities of CatchUp2 for marketplace customer service operations, from simple deal discovery to complex multi-step booking processes with integrated communications.
