"""
CatchUp2 Planning Tools

This module contains enhanced planning tools that integrate with the research_agent
pattern while adding user notification capabilities for plans and progress updates.

Key Features:
- Enhanced write_todos tool with automatic user notifications
- Progress tracking with real-time user updates
- Integration with communication preferences
"""

import json
from typing import List, Dict, Any, Optional, Annotated
from datetime import datetime
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.types import Command
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import InjectedState

from deepagents.state import Todo
from CatchUp2.state import CatchUp2State
from shared.logger import logger
from .communication_tools import send_plan_to_user


@tool(description="""Create and manage TODO lists for complex operations with automatic user notifications.

Use this tool when:
1. Complex multi-step tasks - When a task requires 3 or more distinct steps or actions
2. Non-trivial and complex tasks - Tasks that require careful planning or multiple operations
3. User explicitly requests todo list - When the user directly asks you to use the todo list
4. User provides multiple tasks - When users provide a list of things to be done
5. Multi-service booking processes (e.g., booking both haircut and manicure)
6. Deal search with multiple criteria (category + location + time + price filtering)
7. Communication workflows involving multiple channels (email + WhatsApp)

This enhanced version automatically sends plan notifications to users via their preferred communication method.

Args:
    todos: List of Todo items with content and status
    plan_title: Optional title for the plan (will be generated if not provided)
    send_notification: Whether to send plan notification to user (default: True)
    estimated_duration: Optional estimated duration for the entire plan
""")
def write_todos_with_notification(
    todos: List[Todo], 
    tool_call_id: Annotated[str, InjectedToolCallId],
    state: Annotated[CatchUp2State, InjectedState],
    plan_title: Optional[str] = None,
    send_notification: bool = True,
    estimated_duration: Optional[str] = None
) -> Command:
    """
    Enhanced write_todos tool that automatically sends plan notifications to users.
    
    This tool extends the standard write_todos functionality by automatically
    sending detailed plan notifications to users when complex operations are
    initiated, keeping them informed about what will happen.
    """
    try:
        logger.info(f"Creating TODO list with {len(todos)} tasks, notification: {send_notification}")
        
        # Generate plan title if not provided
        if not plan_title and todos:
            # Create a descriptive title based on the first few tasks
            if len(todos) == 1:
                plan_title = todos[0]['content']
            elif len(todos) <= 3:
                plan_title = f"{todos[0]['content']} and {len(todos)-1} more task{'s' if len(todos) > 2 else ''}"
            else:
                plan_title = f"{todos[0]['content']} and {len(todos)-1} more tasks"
        
        # Send notification to user if enabled and user_id is available
        notification_result = None
        if send_notification and state.user_id and todos:
            try:
                # Prepare tasks for notification (add estimated time if available)
                notification_tasks = []
                for todo in todos:
                    task_dict = {
                        'content': todo['content'],
                        'status': todo.get('status', 'pending')
                    }
                    # Add estimated time if available in todo content
                    if '(' in todo['content'] and ')' in todo['content']:
                        # Extract estimated time from content if present
                        content_parts = todo['content'].split('(')
                        if len(content_parts) > 1 and ')' in content_parts[-1]:
                            time_part = content_parts[-1].split(')')[0]
                            if any(time_word in time_part.lower() for time_word in ['min', 'hour', 'sec']):
                                task_dict['estimated_time'] = time_part
                                task_dict['content'] = '('.join(content_parts[:-1]).strip()
                    
                    notification_tasks.append(task_dict)
                
                # Send plan notification
                notification_result = send_plan_to_user.invoke({
                    "user_id": state.user_id,
                    "plan_title": plan_title or "Your Action Plan",
                    "tasks": notification_tasks,
                    "estimated_duration": estimated_duration,
                    "communication_method": "auto"
                })
                
                logger.info(f"Plan notification sent to user {state.user_id}")
                
            except Exception as e:
                logger.error(f"Failed to send plan notification: {e}")
                # Continue with TODO creation even if notification fails
                notification_result = f"Notification failed: {str(e)}"
        
        # Create the standard TODO update command
        tool_message_content = f"Updated todo list to {todos}"
        if notification_result:
            try:
                notification_data = json.loads(notification_result)
                if notification_data.get('success'):
                    tool_message_content += f" and sent plan notification to user"
                else:
                    tool_message_content += f" (notification failed: {notification_data.get('error', 'unknown error')})"
            except:
                tool_message_content += f" (notification status: {notification_result})"
        
        return Command(
            update={
                "todos": todos,
                "messages": [
                    ToolMessage(tool_message_content, tool_call_id=tool_call_id)
                ],
            }
        )
        
    except Exception as e:
        logger.error(f"Error in write_todos_with_notification: {e}")
        # Fallback to standard behavior
        return Command(
            update={
                "todos": todos,
                "messages": [
                    ToolMessage(f"Updated todo list to {todos} (notification error: {str(e)})", tool_call_id=tool_call_id)
                ],
            }
        )


@tool(description="""Update TODO task status and send progress notifications to users.

Use this tool to mark tasks as completed, in progress, or pending while automatically
sending progress updates to users via their preferred communication method.

Args:
    task_index: Index of the task to update (0-based)
    new_status: New status for the task ('pending', 'in_progress', 'completed')
    send_notification: Whether to send progress notification to user (default: True)
    next_task_index: Optional index of the next task to start (will be marked as in_progress)
""")
def update_todo_with_notification(
    task_index: int,
    new_status: str,
    state: Annotated[CatchUp2State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
    send_notification: bool = True,
    next_task_index: Optional[int] = None
) -> Command:
    """
    Update TODO task status and send progress notifications to users.
    
    This tool allows updating individual task statuses while automatically
    sending progress notifications to keep users informed about task completion
    and what's happening next.
    """
    try:
        logger.info(f"Updating task {task_index} to {new_status}, notification: {send_notification}")
        
        # Get current todos from state
        current_todos = getattr(state, 'todos', [])
        if not current_todos or task_index >= len(current_todos):
            return Command(
                update={
                    "messages": [
                        ToolMessage(f"Error: Task index {task_index} not found in TODO list", tool_call_id=tool_call_id)
                    ],
                }
            )
        
        # Update the task status
        updated_todos = current_todos.copy()
        updated_todos[task_index]['status'] = new_status
        
        # Update next task if specified
        next_task_content = None
        if next_task_index is not None and next_task_index < len(updated_todos):
            updated_todos[next_task_index]['status'] = 'in_progress'
            next_task_content = updated_todos[next_task_index]['content']
        
        # Send progress notification if enabled and task was completed
        notification_result = None
        if send_notification and state.user_id and new_status == 'completed':
            try:
                # Calculate progress percentage
                completed_tasks = sum(1 for todo in updated_todos if todo['status'] == 'completed')
                total_tasks = len(updated_todos)
                progress_percentage = int((completed_tasks / total_tasks) * 100) if total_tasks > 0 else 0
                
                # Send progress update
                from .communication_tools import send_progress_update_to_user
                notification_result = send_progress_update_to_user.invoke({
                    "user_id": state.user_id,
                    "task_completed": current_todos[task_index]['content'],
                    "task_started": next_task_content,
                    "progress_percentage": progress_percentage,
                    "communication_method": "auto"
                })
                
                logger.info(f"Progress notification sent to user {state.user_id}")
                
            except Exception as e:
                logger.error(f"Failed to send progress notification: {e}")
                notification_result = f"Notification failed: {str(e)}"
        
        # Create tool message
        task_content = current_todos[task_index]['content']
        tool_message_content = f"Updated task '{task_content}' to {new_status}"
        
        if next_task_content:
            tool_message_content += f" and started '{next_task_content}'"
        
        if notification_result:
            try:
                notification_data = json.loads(notification_result)
                if notification_data.get('success'):
                    tool_message_content += f" and sent progress notification to user"
                else:
                    tool_message_content += f" (notification failed: {notification_data.get('error', 'unknown error')})"
            except:
                tool_message_content += f" (notification status: {notification_result})"
        
        return Command(
            update={
                "todos": updated_todos,
                "messages": [
                    ToolMessage(tool_message_content, tool_call_id=tool_call_id)
                ],
            }
        )
        
    except Exception as e:
        logger.error(f"Error in update_todo_with_notification: {e}")
        return Command(
            update={
                "messages": [
                    ToolMessage(f"Error updating task: {str(e)}", tool_call_id=tool_call_id)
                ],
            }
        )


@tool(description="""Send plan completion notification when all tasks are finished.

Use this tool when all tasks in a plan have been completed to send a comprehensive
completion notification to the user with a summary of what was accomplished.

Args:
    plan_title: Title of the completed plan
    summary: Brief summary of what was accomplished
    results: List of key results or outcomes
""")
def complete_plan_with_notification(
    plan_title: str,
    summary: str,
    results: List[str],
    state: Annotated[CatchUp2State, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """
    Send plan completion notification when all tasks are finished.
    
    This tool sends a comprehensive completion notification to users when
    a complex plan has been fully executed, providing closure and summary.
    """
    try:
        logger.info(f"Sending plan completion notification for '{plan_title}' to user {state.user_id}")
        
        notification_result = None
        if state.user_id:
            try:
                from .communication_tools import send_plan_completion_to_user
                notification_result = send_plan_completion_to_user.invoke({
                    "user_id": state.user_id,
                    "plan_title": plan_title,
                    "summary": summary,
                    "results": results,
                    "communication_method": "auto"
                })
                
                logger.info(f"Plan completion notification sent to user {state.user_id}")
                
            except Exception as e:
                logger.error(f"Failed to send completion notification: {e}")
                notification_result = f"Notification failed: {str(e)}"
        
        # Create tool message
        tool_message_content = f"Plan '{plan_title}' completed successfully"
        
        if notification_result:
            try:
                notification_data = json.loads(notification_result)
                if notification_data.get('success'):
                    tool_message_content += f" and sent completion notification to user"
                else:
                    tool_message_content += f" (notification failed: {notification_data.get('error', 'unknown error')})"
            except:
                tool_message_content += f" (notification status: {notification_result})"
        
        return Command(
            update={
                "messages": [
                    ToolMessage(tool_message_content, tool_call_id=tool_call_id)
                ],
            }
        )
        
    except Exception as e:
        logger.error(f"Error in complete_plan_with_notification: {e}")
        return Command(
            update={
                "messages": [
                    ToolMessage(f"Error sending completion notification: {str(e)}", tool_call_id=tool_call_id)
                ],
            }
        )
