"""Query enhancement node for CatchUp.

This node analyzes user input and rewrites it in a more comprehensive and concise form
while preserving the original meaning and intent.
"""

import os
from typing import Any, Dict
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import StreamWriter

from catchup.state import CatchUpState
from catchup.prompts import create_query_enhancement_prompt
from shared import create_llm, logger


async def enhance_query(state: CatchUpState, writer: StreamWriter, config: RunnableConfig) -> Dict[str, Any]:
    """Enhance the user's query to be more comprehensive and actionable.
    
    This node takes the latest user message and uses an LLM to analyze and rewrite it
    in a more structured and comprehensive form while preserving the original intent.
    The enhanced query is stored in the 'query' field of the state.
    
    Args:
        state: The current CatchUpState containing messages and other context
        writer: StreamWriter for potential streaming output
        config: Configuration for the node execution
        
    Returns:
        Dict containing the enhanced query to update the state
    """
    
    # Get the latest user message
    messages = state.get("messages", [])
    if not messages:
        # No messages to enhance, return empty query
        return {"query": ""}

    # Find the last human message
    last_human_message = None
    for msg in reversed(messages):
        # Check if it's a message object with type attribute
        if hasattr(msg, 'type') and msg.type == 'human':
            last_human_message = msg
            break
        # Check if it's a dictionary with type key
        elif isinstance(msg, dict) and msg.get('type') == 'human':
            last_human_message = msg
            break
        # Check if it's a HumanMessage instance
        elif hasattr(msg, '__class__') and 'Human' in msg.__class__.__name__:
            last_human_message = msg
            break
    if not last_human_message:
        # No human message found
        return {"query": ""}

    # Get content from message (handle both object and dict formats)
    if hasattr(last_human_message, 'content'):
        message_content = last_human_message.content
    elif isinstance(last_human_message, dict):
        message_content = last_human_message.get('content', '')
    else:
        message_content = str(last_human_message)

    if not message_content:
        # No human message found or empty content
        return {"query": ""}
    
    try:
        # Get configuration
        configuration = config.get("configurable", {})
        model_name = configuration.get("model_name", "anthropic/claude-3.5-sonnet")
        
        # Create LLM instance (without tools for this simple enhancement task)
        llm = create_llm(model_name)
        
        # Create the enhancement prompt
        system_prompt = create_query_enhancement_prompt()
        
        # Prepare messages for the enhancement LLM
        enhancement_messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"Query: {message_content}")
        ]
        
        # Get the enhanced query
        response = await llm.ainvoke(enhancement_messages)
        enhanced_query = response.content.strip()
        logger.info(f"Enhanced query: {enhanced_query}")
        # Store the enhanced query in state
        return {"query": enhanced_query }
        
    except Exception as e:
        logger.exception(f"Error in enhance_query: {e}")
        # Fallback to original message content if enhancement fails
        return {"query": message_content}
