"""
CatchUp2 Sub-agents

This module defines specialized sub-agents for different CatchUp2 functionalities
following the research_agent sub-agent pattern. Each sub-agent is designed to handle
specific aspects of the marketplace customer service operations.

Sub-agents included:
- Booking Agent: Handles booking creation, management, and modifications
- Deal Search Agent: Manages deal discovery and filtering
- Communication Agent: Handles email and WhatsApp communications
- User Management Agent: Manages user profiles and preferences
"""

from typing import Dict, Any, List
from .prompts import (
    create_booking_agent_prompt,
    create_deal_search_agent_prompt,
    create_communication_agent_prompt,
    create_user_management_agent_prompt
)


def create_booking_sub_agent() -> Dict[str, Any]:
    """
    Create the booking management sub-agent.
    
    This sub-agent specializes in handling all booking-related operations including
    creating new bookings, retrieving booking details, managing modifications,
    and handling booking confirmations.
    
    Returns:
        Dictionary containing sub-agent configuration
    """
    return {
        "name": "booking-agent",
        "description": """Used for complex booking operations and management. Call this agent when users want to:
        - Create new bookings for deals or services
        - Retrieve details about existing bookings
        - Modify or cancel existing bookings
        - Get booking confirmations and status updates
        - Handle booking-related issues or questions
        
        This agent has specialized knowledge of booking validation, availability checking,
        and booking lifecycle management.""",
        "prompt": create_booking_agent_prompt(),
        "tools": [
            "get_booking_details",
            "create_booking", 
            "get_deals",
            "get_user_details_by_id",
            "get_business_details"
        ]
    }


def create_deal_search_sub_agent() -> Dict[str, Any]:
    """
    Create the deal search and discovery sub-agent.
    
    This sub-agent specializes in advanced deal discovery, filtering, and
    recommendation operations for the marketplace platform.
    
    Returns:
        Dictionary containing sub-agent configuration
    """
    return {
        "name": "deal-search-agent", 
        "description": """Used for advanced deal discovery and filtering operations. Call this agent when users want to:
        - Search for deals by category, location, or specific criteria
        - Get recommendations based on user preferences and history
        - Filter deals by availability, pricing, or time slots
        - Compare multiple deals and their features
        - Find deals with specific discounts or promotions
        
        This agent has specialized knowledge of deal availability logic, category matching,
        and location-based filtering.""",
        "prompt": create_deal_search_agent_prompt(),
        "tools": [
            "get_all_categories",
            "search_deals",
            "get_deals_by_categoryId",  # Synchronized with CatchUp naming
            "get_deals",
            "get_business_details"  # MCP tool
        ]
    }


def create_communication_sub_agent() -> Dict[str, Any]:
    """
    Create the communication management sub-agent.
    
    This sub-agent specializes in handling all communication operations including
    email composition, WhatsApp messaging, and notification management.
    
    Returns:
        Dictionary containing sub-agent configuration
    """
    return {
        "name": "communication-agent",
        "description": """Used for email and WhatsApp communication operations. Call this agent when users want to:
        - Send booking confirmations via email or WhatsApp
        - Receive promotional offers and notifications
        - Update communication preferences
        - Send custom messages or notifications
        - Handle communication delivery issues
        
        This agent has specialized knowledge of message composition, user communication
        preferences, and multi-channel delivery management.""",
        "prompt": create_communication_agent_prompt(),
        "tools": [
            "sent_email_to_users",  # MCP tool (synchronized with CatchUp)
            "whatsapps_sent_tool",  # MCP tool (synchronized with CatchUp)
            "get_user_details_by_id",
            "get_booking_details"  # MCP tool
        ]
    }


def create_user_management_sub_agent() -> Dict[str, Any]:
    """
    Create the user management sub-agent.
    
    This sub-agent specializes in user profile management, preferences,
    and account-related operations.
    
    Returns:
        Dictionary containing sub-agent configuration
    """
    return {
        "name": "user-management-agent",
        "description": """Used for user profile and account management operations. Call this agent when users want to:
        - View or update their profile information
        - Manage account preferences and settings
        - Access their booking history and account details
        - Update location or contact information
        - Handle account-related questions or issues
        
        This agent has specialized knowledge of user data management, privacy settings,
        and account security.""",
        "prompt": create_user_management_agent_prompt(),
        "tools": [
            "get_user_details_by_id",
            "get_chat_history"  # MCP tool
        ]
    }


def create_query_enhancement_sub_agent() -> Dict[str, Any]:
    """
    Create the query enhancement sub-agent.
    
    This sub-agent specializes in understanding and enhancing user queries
    to make them more actionable for the marketplace system.
    
    Returns:
        Dictionary containing sub-agent configuration
    """
    return {
        "name": "query-enhancement-agent",
        "description": """Used for understanding and enhancing user queries. Call this agent when:
        - User queries are vague or need clarification
        - Need to extract specific intent from natural language
        - Want to improve query structure for better tool selection
        - Need to identify key parameters (location, time, category) from user input
        
        This agent specializes in natural language understanding and query optimization.""",
        "prompt": """You are a query enhancement specialist for the CatchUp marketplace platform.

Your role is to:
1. Analyze user queries and extract key information
2. Identify user intent and required actions
3. Structure queries for optimal tool selection
4. Clarify ambiguous requests while preserving user intent
5. Extract temporal, location, and service-specific details

Always maintain the user's original language and preserve their intent while making
the query more actionable for the marketplace system.""",
        "tools": [
            "get_all_categories",
            "get_user_details_by_id"
        ]
    }


def get_all_catchup2_sub_agents() -> List[Dict[str, Any]]:
    """
    Get all available sub-agents for CatchUp2.
    
    This function returns a list of all specialized sub-agents that can be used
    by the main CatchUp2 agent for handling complex marketplace operations.
    
    Returns:
        List of sub-agent configurations
    """
    return [
        create_booking_sub_agent(),
        create_deal_search_sub_agent(), 
        create_communication_sub_agent(),
        create_user_management_sub_agent(),
        create_query_enhancement_sub_agent()
    ]


def get_sub_agent_by_name(name: str) -> Dict[str, Any]:
    """
    Get a specific sub-agent by name.
    
    Args:
        name: Name of the sub-agent to retrieve
        
    Returns:
        Sub-agent configuration dictionary or None if not found
    """
    all_agents = get_all_catchup2_sub_agents()
    
    for agent in all_agents:
        if agent["name"] == name:
            return agent
    
    return None


def get_recommended_sub_agent(user_intent: str, query_context: Dict[str, Any]) -> str:
    """
    Recommend the most appropriate sub-agent based on user intent and context.
    
    This function analyzes the user's intent and query context to recommend
    the most suitable sub-agent for handling the request.
    
    Args:
        user_intent: Detected user intent
        query_context: Additional context about the query
        
    Returns:
        Name of the recommended sub-agent
    """
    # Map user intents to appropriate sub-agents
    intent_mapping = {
        "book_service": "booking-agent",
        "view_bookings": "booking-agent", 
        "find_service": "deal-search-agent",
        "ask_offer_info": "deal-search-agent",
        "request_help": "user-management-agent",
        "generic_chat": "user-management-agent"
    }
    
    # Check for communication-related keywords in context
    communication_keywords = ["email", "whatsapp", "send", "notification", "message"]
    if any(keyword in str(query_context).lower() for keyword in communication_keywords):
        return "communication-agent"
    
    # Check for booking-related keywords
    booking_keywords = ["booking", "reserve", "appointment", "cancel", "modify"]
    if any(keyword in str(query_context).lower() for keyword in booking_keywords):
        return "booking-agent"
    
    # Check for deal search keywords
    deal_keywords = ["deal", "offer", "discount", "search", "find", "category"]
    if any(keyword in str(query_context).lower() for keyword in deal_keywords):
        return "deal-search-agent"
    
    # Default based on intent
    return intent_mapping.get(user_intent, "user-management-agent")
