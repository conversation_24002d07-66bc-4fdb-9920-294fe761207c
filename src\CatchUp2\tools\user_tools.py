"""
CatchUp2 User Management Tools

This module contains tools for user management operations including user profile
retrieval, chat history access, and user preference management.

All tools maintain compatibility with the existing CatchUp system while providing
enhanced functionality for the research_agent architecture.
SQL queries and database interactions are synchronized with CatchUp project.
"""

from typing import Op<PERSON>, <PERSON><PERSON>
from langchain_core.tools import tool
from langgraph.config import get_stream_writer
from ..supabase.client import supabase
from ..Models.model import UserDetails


@tool
def get_user_details_by_id(
    user_id: str,) -> Tuple[Optional[UserDetails], Optional[str]]:
    """Fetch user table data from a specified table - synchronized with CatchUp.

    Args:
        user_id: The ID of the user to get data from
    Returns:
        Tuple containing (user_data, error) where user_data is a validated
        UserDetails model if successful, None if failed.
    """
    try:
        stream_writer = get_stream_writer()
        stream_writer({"custom_tool_call": f"invoking get_user_details_by_id for {user_id}"})
        response = supabase.table('user_details').select('*').eq('id', user_id).maybe_single().execute()

        # Extract data from response
        if response.data:
            user_data = UserDetails(**response.data[0])  # Assuming single user
            return user_data, None
        else:
            return None, "No user found with the provided ID"

    except Exception as e:
        print(f"Function error: {e}")
        return None, str(e)


# Additional user tools for CatchUp2 functionality (these will use MCP tools from CatchUp)
# get_chat_history, update_user_preferences, get_user_bookings, get_user_location will be imported from MCP tools
