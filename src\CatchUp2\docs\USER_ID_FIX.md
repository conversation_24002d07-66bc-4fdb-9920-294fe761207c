# User ID Access Fix for CatchUp2 Marketplace Tools

## Issue Description

The CatchUp2 marketplace tools were experiencing an issue where the system was trying to pass "current_user" as the `user_id` parameter to tools like `search_deals`, instead of the actual user ID from the state.

**Error Example:**
```
search_deals
ID
category_name	Launch Bar
user_id	current_user
```

## Root Cause

The marketplace tools were defined to take `user_id` as a direct parameter:

```python
@tool
def search_deals(
    category_name: str,
    user_id: str  # ❌ Direct parameter - causes issues
) -> Tuple[Optional[List[Deal]], Optional[str]]:
```

However, in the deepagents/research_agent architecture, tools should access state fields using the `InjectedState` pattern rather than requiring them as direct parameters.

## Solution

### 1. Updated Tool Signatures

Modified the marketplace tools to use the `InjectedState` pattern:

```python
@tool
def search_deals(
    category_name: str,
    *,
    state: Annotated[CatchUp2State, InjectedState]  # ✅ Access state via injection
) -> Tuple[Optional[List[Deal]], Optional[str]]:
```

### 2. Updated State Access

Changed how tools access the user_id:

```python
# ❌ Before: user_id was passed as parameter
def search_deals(category_name: str, user_id: str):
    # Query with user_id parameter
    response = supabase.table('deals_dealid_view').select('*').eq('category_name', category_name).neq('owner_id', user_id).execute()

# ✅ After: user_id is extracted from state
def search_deals(category_name: str, *, state: Annotated[CatchUp2State, InjectedState]):
    # Get user_id from state
    user_id = state.get("user_id")
    if not user_id:
        return None, "Error: user_id not found in state"
    
    # Query with user_id from state
    response = supabase.table('deals_dealid_view').select('*').eq('category_name', category_name).neq('owner_id', user_id).execute()
```

### 3. Added Required Imports

```python
from typing import List, Optional, Tuple, Annotated
from langgraph.prebuilt import InjectedState
from ..state import CatchUp2State
```

## Fixed Tools

The following tools were updated to use the InjectedState pattern:

1. **`search_deals`** - Searches for deals by category name, excluding user's own deals
2. **`get_deals_by_categoryId`** - Searches for deals by category ID, excluding user's own deals

## Benefits

1. **Correct User Identification**: Tools now access the actual user_id from state instead of placeholder values
2. **Architecture Compliance**: Tools follow the deepagents/research_agent pattern correctly
3. **Better Error Handling**: Tools can detect when user_id is missing from state
4. **Maintainability**: Consistent pattern across all tools

## Testing

Created comprehensive tests to verify the fix:

- `test_marketplace_tools_fix.py` - Unit tests for the updated tools
- `test_user_id_fix_demo.py` - Demo showing the fix resolves the original issue

All tests pass and confirm that:
- Tools correctly access user_id from state
- User's own deals are properly excluded from search results
- Error handling works when user_id is missing

## Usage

The tools now work seamlessly with the CatchUp2 agent:

```python
# Create state with user_id
state = create_catchup2_state(
    user_id="real_user_123",
    session_id="session_456"
)

# Tools automatically access user_id from state
deals, error = search_deals("Launch Bar", state=state)
```

## Backward Compatibility

This change maintains full backward compatibility with the existing CatchUp2 system while fixing the user identification issue.
