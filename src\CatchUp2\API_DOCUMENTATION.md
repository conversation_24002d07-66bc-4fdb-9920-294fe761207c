# CatchUp2 API Documentation

This document provides comprehensive API documentation for the CatchUp2 agent, including all tools, sub-agents, and integration points.

## Table of Contents

1. [Core Agent API](#core-agent-api)
2. [Marketplace Tools](#marketplace-tools)
3. [User Management Tools](#user-management-tools)
4. [Communication Tools](#communication-tools)
5. [Sub-agents API](#sub-agents-api)
6. [State Management](#state-management)
7. [Response Formats](#response-formats)

## Core Agent API

### `create_catchup2_agent(model_name, **kwargs)`

Creates the main CatchUp2 agent with full marketplace functionality.

**Parameters:**
- `model_name` (str): LLM model identifier (default: "anthropic/claude-3.5-sonnet")
- `**kwargs`: Additional LLM configuration parameters

**Returns:**
- `LangGraph Agent`: Configured agent ready for marketplace operations

**Example:**
```python
from CatchUp2 import create_catchup2_agent

agent = create_catchup2_agent(
    model_name="anthropic/claude-3.5-sonnet",
    temperature=0.7
)
```

### `create_simple_catchup2_agent(model_name, **kwargs)`

Creates a simplified agent with essential tools only.

**Parameters:**
- `model_name` (str): LLM model identifier
- `**kwargs`: Additional LLM configuration parameters

**Returns:**
- `LangGraph Agent`: Simplified agent for basic operations

**Use Case:** Suitable for lightweight deployments or specific use cases requiring only core functionality.

## Marketplace Tools

### `get_all_categories()`

Retrieves all available service categories from the marketplace.

**Parameters:** None

**Returns:**
```json
{
  "success": true,
  "categories": [
    {
      "id": "cat_123",
      "name": "Beauty",
      "description": "Beauty and wellness services",
      "parent_id": null,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total_count": 15
}
```

**Business Logic:**
- Returns all active categories regardless of business context
- Categories are hierarchically organized with parent-child relationships
- Used for category validation in deal searches

### `search_deals(category_name, location, max_results)`

Searches for deals by category name with optional location filtering.

**Parameters:**
- `category_name` (str): Name of the category to search (case-insensitive)
- `location` (str, optional): Location filter (not yet implemented)
- `max_results` (int): Maximum results to return (default: 10)

**Returns:**
```json
{
  "success": true,
  "category": "Beauty",
  "deals": [
    {
      "id": "deal_456",
      "title": "Premium Haircut",
      "description": "Professional haircut with styling",
      "business_id": "biz_789",
      "discount_percentage": 25.0,
      "original_price": 50.0,
      "discounted_price": 37.5,
      "start_date": "2024-01-01",
      "end_date": "2024-12-31",
      "recurring_days": [1, 2, 3, 4, 5],
      "time_slots": [
        {"start_time": "09:00", "end_time": "18:00"}
      ],
      "status": "active"
    }
  ],
  "total_count": 5
}
```

**Business Logic:**
- Performs case-insensitive category matching
- Only returns active deals available for booking
- Validates category existence before searching

### `get_deals_by_category_id(category_id, max_results)`

Retrieves deals for a specific category using category ID.

**Parameters:**
- `category_id` (str): ID of the category
- `max_results` (int): Maximum results to return (default: 10)

**Returns:** Same format as `search_deals`

### `get_deals(business_id, max_results)`

Retrieves deals, optionally filtered by business.

**Parameters:**
- `business_id` (str, optional): Business ID to filter deals
- `max_results` (int): Maximum results to return (default: 20)

**Returns:** Same format as `search_deals` but without category context

### `get_business_details(business_id)`

Retrieves comprehensive business information.

**Parameters:**
- `business_id` (str): ID of the business

**Returns:**
```json
{
  "success": true,
  "business": {
    "id": "biz_789",
    "name": "Premium Salon",
    "description": "High-end beauty salon",
    "address": "Via Roma 123, Milan",
    "phone": "+39 02 1234567",
    "email": "<EMAIL>",
    "latitude": 45.4642,
    "longitude": 9.1900,
    "categories": ["Beauty", "Wellness"],
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

### `get_booking_details(booking_id)`

Retrieves details about a specific booking.

**Parameters:**
- `booking_id` (str): ID of the booking

**Returns:**
```json
{
  "success": true,
  "booking": {
    "id": "book_101",
    "user_id": "user_123",
    "deal_id": "deal_456",
    "business_id": "biz_789",
    "booking_date": "2024-02-15",
    "booking_time": "14:00",
    "status": "confirmed",
    "notes": "Please call before arrival",
    "created_at": "2024-02-01T10:00:00Z",
    "updated_at": "2024-02-01T10:00:00Z"
  }
}
```

### `create_booking(user_id, deal_id, booking_date, booking_time, notes)`

Creates a new booking for a deal.

**Parameters:**
- `user_id` (str): ID of the user making the booking
- `deal_id` (str): ID of the deal to book
- `booking_date` (str): Date for booking (YYYY-MM-DD format)
- `booking_time` (str, optional): Time for booking (HH:MM format)
- `notes` (str, optional): Additional notes for the booking

**Returns:**
```json
{
  "success": true,
  "booking": {
    "id": "book_102",
    "user_id": "user_123",
    "deal_id": "deal_456",
    "business_id": "biz_789",
    "booking_date": "2024-02-15",
    "booking_time": "14:00",
    "status": "pending",
    "notes": "Created via CatchUp2"
  },
  "message": "Booking created successfully"
}
```

**Business Logic:**
- Validates deal existence and active status
- Automatically sets booking status to "pending"
- Associates booking with the deal's business

## User Management Tools

### `get_user_details_by_id(user_id)`

Retrieves comprehensive user profile information.

**Parameters:**
- `user_id` (str): Unique user identifier

**Returns:**
```json
{
  "success": true,
  "user": {
    "id": "user_123",
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+39 ************",
    "location": {
      "latitude": 45.4642,
      "longitude": 9.1900
    },
    "preferences": {
      "language": "en",
      "communication_method": "email"
    },
    "status": "active",
    "member_since": "2024-01-01T00:00:00Z"
  }
}
```

### `get_chat_history(user_id, session_id, limit)`

Retrieves conversation history for context.

**Parameters:**
- `user_id` (str): ID of the user
- `session_id` (str, optional): Session ID to filter history
- `limit` (int): Maximum messages to return (default: 50)

**Returns:**
```json
{
  "success": true,
  "user_id": "user_123",
  "session_id": "session_456",
  "messages": [
    {
      "id": "msg_001",
      "type": "user",
      "content": "I'm looking for a haircut",
      "timestamp": "2024-02-01T10:00:00Z",
      "session_id": "session_456"
    }
  ],
  "total_count": 10
}
```

### `get_user_bookings(user_id, status, limit)`

Retrieves user's booking history.

**Parameters:**
- `user_id` (str): ID of the user
- `status` (str, optional): Filter by booking status
- `limit` (int): Maximum bookings to return (default: 20)

**Returns:**
```json
{
  "success": true,
  "user_id": "user_123",
  "status_filter": "confirmed",
  "bookings": [
    {
      "id": "book_101",
      "deal_id": "deal_456",
      "business_id": "biz_789",
      "booking_date": "2024-02-15",
      "booking_time": "14:00",
      "status": "confirmed",
      "created_at": "2024-02-01T10:00:00Z"
    }
  ],
  "total_count": 3
}
```

## Communication Tools

### `send_email_to_user(user_id, subject, content, email_type, booking_id, deal_id)`

Sends professional HTML email to a user.

**Parameters:**
- `user_id` (str): ID of the user to send email to
- `subject` (str): Email subject line
- `content` (str): Email content (can include HTML)
- `email_type` (str): Type of email (default: "notification")
- `booking_id` (str, optional): Related booking ID
- `deal_id` (str, optional): Related deal ID

**Returns:**
```json
{
  "success": true,
  "user_id": "user_123",
  "email_address": "<EMAIL>",
  "subject": "Booking Confirmation",
  "email_type": "booking",
  "message": "Email sent successfully"
}
```

### `send_whatsapp_message(user_id, message, message_type, booking_id, deal_id)`

Sends WhatsApp message to a user.

**Parameters:**
- `user_id` (str): ID of the user to send message to
- `message` (str): Message content (plain text)
- `message_type` (str): Type of message (default: "notification")
- `booking_id` (str, optional): Related booking ID
- `deal_id` (str, optional): Related deal ID

**Returns:**
```json
{
  "success": true,
  "user_id": "user_123",
  "phone_number": "+39 ************",
  "message_type": "booking",
  "message": "WhatsApp message sent successfully"
}
```

## Sub-agents API

### Available Sub-agents

1. **booking-agent**: Specialized in booking operations
2. **deal-search-agent**: Advanced deal discovery and filtering
3. **communication-agent**: Email and WhatsApp communications
4. **user-management-agent**: User profile and preference management
5. **query-enhancement-agent**: Query understanding and optimization

### Sub-agent Usage

Sub-agents are automatically invoked by the main agent based on user intent and query context. They can also be explicitly called using the `task` tool:

```python
# Example of explicit sub-agent invocation
response = agent.invoke({
    "messages": [HumanMessage(content="task: Use booking-agent to create a booking for deal_456")]
})
```

## State Management

### CatchUp2State Structure

```python
class CatchUp2State(DeepAgentState):
    # Core conversation
    messages: List[AnyMessage]
    
    # User and session context
    user_context: UserContext
    session_config: SessionConfig
    
    # Conversation tracking
    current_phase: ConversationPhase
    conversation_metrics: ConversationMetrics
    
    # Intent and routing
    detected_intent: Optional[UserIntent]
    intent_confidence: Optional[float]
    
    # Query processing
    original_query: Optional[str]
    enhanced_query: Optional[str]
    
    # Marketplace context
    business_context: Optional[Dict[str, Any]]
    current_deals: Optional[List[Dict[str, Any]]]
    active_bookings: Optional[List[Dict[str, Any]]]
```

## Response Formats

### Chat Mode (is_chat=True)

Returns markdown-formatted responses directly:

```
I found 3 haircut deals in Milan:

1. **Premium Haircut** at Premium Salon
   - 25% discount (€37.50 instead of €50.00)
   - Available Monday-Friday, 9:00-18:00

2. **Quick Cut** at Express Salon
   - 15% discount (€25.50 instead of €30.00)
   - Available daily, 8:00-20:00
```

### API Mode (is_chat=False)

Returns structured JSON responses:

```json
{
  "llmResponse": "I found 3 haircut deals in Milan...",
  "llmResponseIntent": "available_deals",
  "userIntent": "find_service",
  "relatedIds": ["deal_456", "deal_789", "deal_012"]
}
```

## Error Handling

All tools return consistent error formats:

```json
{
  "error": "Description of the error",
  "success": false,
  "additional_context": "Optional additional information"
}
```

Common error scenarios:
- Database connectivity issues
- Invalid user or deal IDs
- Missing required parameters
- Permission or privacy violations
- Service unavailability
